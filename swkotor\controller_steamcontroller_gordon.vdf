"controller_mappings"
{
	"version"		"3"
	"revision"		"106"
	"title"		"#Title"
	"description"		"#SettingsController_AutosaveDescription"
	"creator"		"76561198314857930"
	"progenitor"		""
	"url"		"autosave://C:\\Steam\\steamapps\\common\\Steam Controller Configs\\354592202\\config\\swkotor\\controller_steamcontroller_gordon.vdf"
	"export_type"		"unknown"
	"controller_type"		"controller_steamcontroller_gordon"
	"controller_caps"		"2179061"
	"major_revision"		"0"
	"minor_revision"		"0"
	"Timestamp"		"0"
	"actions"
	{
		"Default"
		{
			"title"		"Default"
			"legacy_set"		"1"
		}
	}
	"action_layers"
	{
		"Preset_1000002"
		{
			"title"		"quick save/load"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
	}
	"localization"
	{
		"english"
		{
			"title"		"Gamepad With Camera Controls"
			"description"		"This template is for most games that already have built-in gamepad support and have a first or third person controlled camera.  FPS or Third Person Adventure games, etc."
		}
		"czech"
		{
			"title"		"Gamepad s ovládáním kamery"
			"description"		"Tato šablona je pro většinu her podporujících gamepad a disponujících kamerou z pohledu první nebo třetí osoby. Mezi takové hry patří například akční hry z pohledu první nebo třetí osoby."
		}
		"danish"
		{
			"title"		"Gamepad med kamerastyring"
			"description"		"Denne skabelon er til de fleste spil, der allerede har indbygget gamepad-understøttelse og har et første- eller tredjepersonskontrolleret kamera. FPS eller tredjepersons adventure-spil osv."
		}
		"dutch"
		{
			"title"		"Gamepad met camerabesturing"
			"description"		"Deze template is voor de meeste spellen die reeds ingebouwde gamepadondersteuning hebben en die een camera hebben die wordt bestuurd in de eerste of derde persoon. FPS, third person-avontuurspellen, etc."
		}
		"finnish"
		{
			"title"		"Kameraa ohjaava peliohjain"
			"description"		"Tämä malli on useimmille muita ohjaimia valmiiksi tukeville peleille, joissa on ensimmäisessä tai kolmannessa persoonassa ohjattava kamera. FPS-pelit, kolmannen persoonan seikkailupelit jne."
		}
		"french"
		{
			"title"		"Manette avec contrôles caméra"
			"description"		"Ce modèle fonctionne pour la plupart des jeux ayant un support manette intégré et une caméra contrôlée à la première ou à la troisième personne. FPS, jeux d'aventure à la troisième personne, etc."
		}
		"german"
		{
			"title"		"Gamepad mit Kamerasteuerung"
			"description"		"Diese Vorlage ist für die meisten Spiele konzipiert, die bereits volle Untersützung für Gamepads mit sich bringen und eine First- oder Third-Person-Kamerasteuerung haben. Gedacht für Ego-Shooter, Third-Person-Abenteuerspiele usw."
		}
		"hungarian"
		{
			"title"		"Gamepad kamerairányítással"
			"description"		"Ez a sablon a legtöbb olyan játékhoz való, melyek már rendelkeznek beépített gamepad-támogatással, és van első vagy harmadik személyű kezelésű kamerájuk. Ilyenek az FPS vagy harmadik személyű kalandjátékok stb."
		}
		"italian"
		{
			"title"		"Gamepad con controlli della telecamera"
			"description"		"Questo template è pensato per la maggior parte dei giochi che hanno già il supporto per gamepad integrato e hanno la visuale controllata in prima o terza persona. Giochi d'avventura in terza persona, FPS ecc."
		}
		"japanese"
		{
			"title"		"カメラコントロール機能を持つゲームパッド"
			"description"		"FPS や、アドベンチャーゲームのような、一人称または三人称のカメラ操作を行うゲームパッドに標準対応したゲーム用のテンプレートです。"
		}
		"koreana"
		{
			"title"		"카메라 조작 기능이 있는 게임패드"
			"description"		"이 템플릿은 이미 게임패드 지원이 내장되어 있으며 1인칭 또는 3인칭 시점 카메라 조작을 지원하는 대부분의 게임을 위한 것입니다. FPS, 3인칭 어드벤쳐 게임 및 기타."
		}
		"polish"
		{
			"title"		"Kontroler obsługujący kamerę"
			"description"		"Ten szablon jest dla większości gier, które mają wbudowane wsparcie dla kontrolerów, a także kamerę kontrolowaną z perspektywy pierwszej lub trzeciej osoby, np. FPS-y bądź gry przygodowe."
		}
		"portuguese"
		{
			"title"		"Comando com controlos de câmara"
			"description"		"Este modelo é indicado para jogos que já têm compatibilidade nativa com comando e têm uma câmara que pode ser controlada. Por exemplo, jogos em primeira ou terceira pessoa, do género de aventura, de tiros, etc."
		}
		"romanian"
		{
			"title"		"Gamepad cu controale pentru cameră"
			"description"		"Acest șablon este pentru majoritatea jocurilor care au deja suport pentru gamepad implementat și au o cameră controlată din perspectivă first sau third person. FPS sau jocuri de aventură third person, etc."
		}
		"russian"
		{
			"title"		"Геймпад с управлением камерой"
			"description"		"Этот шаблон предназначен для большинства игр от первого или третьего лица, в которых уже есть встроенная поддержка геймпада (например, для шутеров или экшенов)."
		}
		"spanish"
		{
			"title"		"Mando con controles de cámara"
			"description"		"Esta plantilla es para la mayoría de juegos que ya incluyen de serie compatibilidad con mando y disponen de cámara controlada en primera o tercera persona: FPS, juegos de aventura en tercera persona, etc."
		}
		"swedish"
		{
			"title"		"Gamepad med kamerakontroller"
			"description"		"Denna mall är för de flesta spel som redan har inbyggt stöd för spelkontroller och har en kamera som styrs i första- eller tredjeperson. FPS eller äventyrsspel etc."
		}
		"schinese"
		{
			"title"		"支持视角控制的手柄"
			"description"		"该模板适用于已内置手柄支持，并且拥有第一或第三人称控制视角的大多数游戏。包括 FPS 或第三人称冒险游戏等。"
		}
		"thai"
		{
			"title"		"เกมแพดพร้อมการควบคุมมุมกล้อง"
			"description"		"แม่แบบนี้ใช้สำหรับเกมส่วนมากที่มีการรองรับเกมแพดมาในตัวอยู่แล้ว และมีการควบคุมมุมกล้องในมุมมองบุคคลที่หนึ่งหรือสาม เช่น เกมยิงมุมมองบุคคลที่หนึ่ง หรือเกมผจญภัยมุมมองบุคคลที่สาม ฯลฯ"
		}
		"brazilian"
		{
			"title"		"Controle com controle de câmera"
			"description"		"Este modelo é para jogos já compatíveis com controles que possuem uma câmera controlável, seja em primeira ou terceira pessoa, como jogos de tiro, aventura, etc."
		}
		"bulgarian"
		{
			"title"		"Геймпад с управление на камерата"
			"description"		"Този шаблон е за повечето игри, които вече имат вградена поддръжка на геймпад и включват управление на камерата от първо или трето лице. Екшъни от първо лице, приключенски игри от трето лице и т.н."
		}
		"greek"
		{
			"title"		"Χειριστήριο με πλήκτρα κάμερας"
			"description"		"Αυτό το πρότυπο είναι για τα περισσότερα παιχνίδια που έχουν ενσωματωμένη υποστήριξη χειριστηρίου και έχουν μια ελεγχόμενη κάμερα πρώτου ή τρίτου προσώπου. FPS ή παιχνίδια περιπέτειας τρίτου προσώπου κλπ."
		}
		"turkish"
		{
			"title"		"Kamera Kontrollü Oyun Kumandası"
			"description"		"Bu şablon hali hazırda oyun içi oyun kumandası desteği ve birincil veya üçüncü kişi kontrollü kameraya sahip oyunlar içindir. FPS veya Üçüncü Kişi Macera oyunları vb."
		}
		"ukrainian"
		{
			"title"		"Ґеймпад з елементами керування камерою"
			"description"		"Цей шаблон для більшості ігор, що вже мають вбудовану підтримку ґеймпада і у яких камера керується від першої або третьої особи. Шутери від першої особи чи пригодницькі ігри від третьої особи тощо."
		}
	}
	"group"
	{
		"id"		"0"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press R, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press Y, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press X, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press M, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"1"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_up, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_down, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_right, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_left, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"2"
		"mode"		"mouse_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"3"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_LEFT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"4"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"edge_binding_radius"		"0"
		}
	}
	"group"
	{
		"id"		"5"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button LEFT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"edge_binding_radius"		"4805"
		}
	}
	"group"
	{
		"id"		"6"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"8"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"sensitivity"		"63"
			"friction"		"1"
			"acceleration"		"3"
			"edge_spin_radius"		"0"
		}
	}
	"group"
	{
		"id"		"9"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"friction"		"1"
			"edge_spin_radius"		"0"
		}
	}
	"group"
	{
		"id"		"10"
		"mode"		"joystick_camera"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"11"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press RIGHT_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press UP_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press DOWN_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"152"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"12"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button LEFT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"13"
		"mode"		"scrollwheel"
		"name"		""
		"description"		""
		"inputs"
		{
			"scroll_clockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_DOWN, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"scroll_counterclockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_UP, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"scroll_angle"		"112"
			"haptic_intensity"		"1"
			"scroll_type"		"2"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"14"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press W, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press S, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press D, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press A, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"requires_click"		"0"
		}
	}
	"group"
	{
		"id"		"15"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_natural_sensitivity"		"141"
		}
	}
	"group"
	{
		"id"		"16"
		"mode"		"radial_menu"
		"name"		""
		"description"		""
		"inputs"
		{
			"touch_menu_button_1"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_2"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_3"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_4"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_5"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 5, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_6"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 6, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"touchmenu_button_fire_type"		"1"
		}
	}
	"group"
	{
		"id"		"17"
		"mode"		"scrollwheel"
		"name"		""
		"description"		""
		"inputs"
		{
			"scroll_clockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_DOWN, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"scroll_counterclockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_UP, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"scroll_type"		"2"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"18"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"31"
		"mode"		"scrollwheel"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"32"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"33"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"34"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"35"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"36"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"37"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"38"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F8, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"39"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"40"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"41"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press RIGHT_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press UP_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press DOWN_ARROW, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"207"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"42"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press UP_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press DOWN_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press RIGHT_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"requires_click"		"0"
		}
	}
	"group"
	{
		"id"		"7"
		"mode"		"switches"
		"name"		""
		"description"		"#Description"
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press ESCAPE, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_menu"
			{
				"activators"
				{
					"Start_Press"
					{
						"bindings"
						{
							"binding"		"controller_action add_layer 2 0 0, , "
						}
					}
					"release"
					{
						"bindings"
						{
							"binding"		"controller_action remove_layer 2 0 0, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press TAB, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press RETURN, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_right"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mode_shift left_trackpad 16"
						}
						"settings"
						{
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_stick_click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mode_shift joystick 42"
						}
						"settings"
						{
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"30"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F4, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"44"
		"mode"		"radial_menu"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"preset"
	{
		"id"		"0"
		"name"		"Default"
		"group_source_bindings"
		{
			"7"		"switch active"
			"1"		"left_trackpad inactive"
			"11"		"left_trackpad inactive"
			"13"		"left_trackpad inactive"
			"16"		"left_trackpad inactive modeshift"
			"17"		"left_trackpad inactive modeshift"
			"18"		"left_trackpad inactive modeshift"
			"41"		"left_trackpad active modeshift"
			"44"		"left_trackpad active"
			"3"		"joystick inactive"
			"14"		"joystick active"
			"42"		"joystick inactive modeshift"
			"5"		"right_trigger active"
			"12"		"right_trigger active modeshift"
			"2"		"right_trackpad inactive"
			"6"		"right_trackpad inactive"
			"8"		"right_trackpad active"
			"9"		"right_trackpad active modeshift"
			"10"		"right_trackpad inactive"
			"0"		"button_diamond active"
			"4"		"left_trigger active"
			"15"		"gyro active"
		}
	}
	"preset"
	{
		"id"		"1"
		"name"		"Preset_1000002"
		"group_source_bindings"
		{
			"30"		"switch active"
			"31"		"left_trackpad active"
			"32"		"left_trackpad active modeshift"
			"38"		"joystick active"
			"33"		"right_trigger active"
			"34"		"right_trigger active modeshift"
			"35"		"right_trackpad active"
			"36"		"right_trackpad active modeshift"
			"37"		"button_diamond active"
			"39"		"left_trigger active"
			"40"		"gyro active"
		}
	}
	"settings"
	{
		"left_trackpad_mode"		"0"
		"right_trackpad_mode"		"0"
		"action_set_trigger_cursor_show"		"0"
		"action_set_trigger_cursor_hide"		"0"
	}
}
