"controller_mappings"
{
	"version"		"3"
	"revision"		"104"
	"title"		"#Title"
	"description"		"#SettingsController_AutosaveDescription"
	"creator"		"76561198314857930"
	"progenitor"		"default://genshinimpact"
	"url"		""
	"export_type"		"unknown"
	"controller_type"		"controller_steamcontroller_gordon"
	"major_revision"		"0"
	"minor_revision"		"0"
	"Timestamp"		"0"
	"actions"
	{
		"Default"
		{
			"title"		"Default"
			"legacy_set"		"1"
		}
	}
	"action_layers"
	{
		"Preset_1000001"
		{
			"title"		"scroll"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
		"Preset_1000002"
		{
			"title"		"gyro on"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
		"Preset_1000003"
		{
			"title"		"interact"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
	}
	"localization"
	{
		"english"
		{
			"title"		"Gamepad With Camera Controls"
			"description"		"This template is for most games that already have built-in gamepad support and have a first or third person controlled camera.  FPS or Third Person Adventure games, etc."
		}
		"czech"
		{
			"title"		"Gamepad s ovládáním kamery"
			"description"		"Tato šablona je pro většinu her podporujících gamepad a disponujících kamerou z pohledu první nebo třetí osoby. Mezi takové hry patří například akční hry z pohledu první nebo třetí osoby."
		}
		"danish"
		{
			"title"		"Gamepad med kamerastyring"
			"description"		"Denne skabelon er til de fleste spil, der allerede har indbygget gamepad-understøttelse og har et første- eller tredjepersonskontrolleret kamera. FPS eller tredjepersons adventure-spil osv."
		}
		"dutch"
		{
			"title"		"Gamepad met camerabesturing"
			"description"		"Deze template is voor de meeste spellen die reeds ingebouwde gamepadondersteuning hebben en die een camera hebben die wordt bestuurd in de eerste of derde persoon. FPS, third person-avontuurspellen, etc."
		}
		"finnish"
		{
			"title"		"Kameraa ohjaava peliohjain"
			"description"		"Tämä malli on useimmille muita ohjaimia valmiiksi tukeville peleille, joissa on ensimmäisessä tai kolmannessa persoonassa ohjattava kamera. FPS-pelit, kolmannen persoonan seikkailupelit jne."
		}
		"french"
		{
			"title"		"Manette avec contrôles caméra"
			"description"		"Ce modèle fonctionne pour la plupart des jeux ayant un support manette intégré et une caméra contrôlée à la première ou à la troisième personne. FPS, jeux d'aventure à la troisième personne, etc."
		}
		"german"
		{
			"title"		"Gamepad mit Kamerasteuerung"
			"description"		"Diese Vorlage ist für die meisten Spiele konzipiert, die bereits volle Untersützung für Gamepads mit sich bringen und eine First- oder Third-Person-Kamerasteuerung haben. Gedacht für Ego-Shooter, Third-Person-Abenteuerspiele usw."
		}
		"hungarian"
		{
			"title"		"Gamepad kamerairányítással"
			"description"		"Ez a sablon a legtöbb olyan játékhoz való, melyek már rendelkeznek beépített gamepad-támogatással, és van első vagy harmadik személyű kezelésű kamerájuk. Ilyenek az FPS vagy harmadik személyű kalandjátékok stb."
		}
		"italian"
		{
			"title"		"Gamepad con controlli della telecamera"
			"description"		"Questo template è pensato per la maggior parte dei giochi che hanno già il supporto per gamepad integrato e hanno la visuale controllata in prima o terza persona. Giochi d'avventura in terza persona, FPS ecc."
		}
		"japanese"
		{
			"title"		"カメラコントロール機能を持つゲームパッド"
			"description"		"FPS や、アドベンチャーゲームのような、一人称または三人称のカメラ操作を行うゲームパッドに標準対応したゲーム用のテンプレートです。"
		}
		"koreana"
		{
			"title"		"카메라 조작 기능이 있는 게임패드"
			"description"		"이 템플릿은 이미 게임패드 지원이 내장되어 있으며 1인칭 또는 3인칭 시점 카메라 조작을 지원하는 대부분의 게임을 위한 것입니다. FPS, 3인칭 어드벤쳐 게임 및 기타."
		}
		"polish"
		{
			"title"		"Kontroler obsługujący kamerę"
			"description"		"Ten szablon jest dla większości gier, które mają wbudowane wsparcie dla kontrolerów, a także kamerę kontrolowaną z perspektywy pierwszej lub trzeciej osoby, np. FPS-y bądź gry przygodowe."
		}
		"portuguese"
		{
			"title"		"Comando com controlos de câmara"
			"description"		"Este modelo é indicado para jogos que já têm compatibilidade nativa com comando e têm uma câmara que pode ser controlada. Por exemplo, jogos em primeira ou terceira pessoa, do género de aventura, de tiros, etc."
		}
		"romanian"
		{
			"title"		"Gamepad cu controale pentru cameră"
			"description"		"Acest șablon este pentru majoritatea jocurilor care au deja suport pentru gamepad implementat și au o cameră controlată din perspectivă first sau third person. FPS sau jocuri de aventură third person, etc."
		}
		"russian"
		{
			"title"		"Геймпад с управлением камерой"
			"description"		"Этот шаблон предназначен для большинства игр от первого или третьего лица, в которых уже есть встроенная поддержка геймпада (например, для шутеров или экшенов)."
		}
		"spanish"
		{
			"title"		"Mando con controles de cámara"
			"description"		"Esta plantilla es para la mayoría de juegos que ya incluyen de serie compatibilidad con mando y disponen de cámara controlada en primera o tercera persona: FPS, juegos de aventura en tercera persona, etc."
		}
		"swedish"
		{
			"title"		"Gamepad med kamerakontroller"
			"description"		"Denna mall är för de flesta spel som redan har inbyggt stöd för spelkontroller och har en kamera som styrs i första- eller tredjeperson. FPS eller äventyrsspel etc."
		}
		"schinese"
		{
			"title"		"支持视角控制的手柄"
			"description"		"该模板适用于已内置手柄支持，并且拥有第一或第三人称控制视角的大多数游戏。包括 FPS 或第三人称冒险游戏等。"
		}
		"thai"
		{
			"title"		"เกมแพดพร้อมการควบคุมมุมกล้อง"
			"description"		"แม่แบบนี้ใช้สำหรับเกมส่วนมากที่มีการรองรับเกมแพดมาในตัวอยู่แล้ว และมีการควบคุมมุมกล้องในมุมมองบุคคลที่หนึ่งหรือสาม เช่น เกมยิงมุมมองบุคคลที่หนึ่ง หรือเกมผจญภัยมุมมองบุคคลที่สาม ฯลฯ"
		}
		"brazilian"
		{
			"title"		"Controle com controle de câmera"
			"description"		"Este modelo é para jogos já compatíveis com controles que possuem uma câmera controlável, seja em primeira ou terceira pessoa, como jogos de tiro, aventura, etc."
		}
		"bulgarian"
		{
			"title"		"Геймпад с управление на камерата"
			"description"		"Този шаблон е за повечето игри, които вече имат вградена поддръжка на геймпад и включват управление на камерата от първо или трето лице. Екшъни от първо лице, приключенски игри от трето лице и т.н."
		}
		"greek"
		{
			"title"		"Χειριστήριο με πλήκτρα κάμερας"
			"description"		"Αυτό το πρότυπο είναι για τα περισσότερα παιχνίδια που έχουν ενσωματωμένη υποστήριξη χειριστηρίου και έχουν μια ελεγχόμενη κάμερα πρώτου ή τρίτου προσώπου. FPS ή παιχνίδια περιπέτειας τρίτου προσώπου κλπ."
		}
		"turkish"
		{
			"title"		"Kamera Kontrollü Oyun Kumandası"
			"description"		"Bu şablon hali hazırda oyun içi oyun kumandası desteği ve birincil veya üçüncü kişi kontrollü kameraya sahip oyunlar içindir. FPS veya Üçüncü Kişi Macera oyunları vb."
		}
		"ukrainian"
		{
			"title"		"Ґеймпад з елементами керування камерою"
			"description"		"Цей шаблон для більшості ігор, що вже мають вбудовану підтримку ґеймпада і у яких камера керується від першої або третьої особи. Шутери від першої особи чи пригодницькі ігри від третьої особи тощо."
		}
	}
	"group"
	{
		"id"		"0"
		"mode"		"four_buttons"
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"controller_action hold_layer 2 0 0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press M"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press B"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"1"
		"mode"		"dpad"
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"2"
		"mode"		"mouse_joystick"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"3"
		"mode"		"joystick_move"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_LEFT"
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"4"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button RIGHT"
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"output_trigger"		"1"
		}
	}
	"group"
	{
		"id"		"5"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button LEFT"
						}
						"settings"
						{
							"hold_repeats"		"1"
							"repeat_rate"		"51"
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button LEFT"
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"output_trigger"		"2"
		}
	}
	"group"
	{
		"id"		"6"
		"mode"		"joystick_move"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT"
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"8"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"9"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"10"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"sensitivity"		"145"
			"mouse_smoothing"		"29"
			"mouse_move_threshold"		"1"
			"mouse_trigger_clamp_amount"		"89"
		}
	}
	"group"
	{
		"id"		"11"
		"mode"		"dpad"
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press W"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press S"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press D"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press A"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_ALT"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"requires_click"		"0"
			"overlap_region"		"4029"
		}
	}
	"group"
	{
		"id"		"12"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"13"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"sensitivity"		"140"
			"friction"		"1"
			"acceleration"		"3"
		}
	}
	"group"
	{
		"id"		"14"
		"mode"		"2dscroll"
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press L"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press C"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"15"
		"mode"		"scrollwheel"
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"17"
		"mode"		"four_buttons"
		"description"		""
		"inputs"
		{
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press V"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F"
						}
						"settings"
						{
							"hold_repeats"		"1"
							"repeat_rate"		"51"
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press V"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"18"
		"mode"		"2dscroll"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"19"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"20"
		"mode"		"dpad"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"21"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"22"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"23"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"24"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"25"
		"mode"		"scrollwheel"
		"description"		""
		"inputs"
		{
			"scroll_clockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_DOWN"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"scroll_counterclockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_UP"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
			"scroll_angle"		"139"
			"scroll_type"		"2"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"26"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"27"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"28"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"29"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"30"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"31"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"32"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"33"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"34"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"35"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"36"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"37"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"38"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"39"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"40"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"41"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"42"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"43"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"44"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"45"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"46"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"47"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"48"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"49"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"50"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"51"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"52"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"53"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"54"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"55"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"56"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"57"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"59"
		"mode"		"2dscroll"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"60"
		"mode"		"four_buttons"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"61"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"62"
		"mode"		"dpad"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"63"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"64"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"65"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
			"gyro_button"		"0"
		}
	}
	"group"
	{
		"id"		"66"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"67"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"68"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"69"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"70"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"71"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"72"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"73"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"74"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"75"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"76"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"77"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"78"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"79"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"80"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"81"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"82"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"83"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"84"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"85"
		"mode"		"2dscroll"
		"description"		""
		"inputs"
		{
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4"
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"1000"
		}
	}
	"group"
	{
		"id"		"86"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"87"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"88"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"89"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"90"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"91"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"92"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"93"
		"mode"		"four_buttons"
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press TAB"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"94"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"95"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"96"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"98"
		"mode"		"2dscroll"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"99"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 5"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"100"
		"mode"		"2dscroll"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"101"
		"mode"		"four_buttons"
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press Z"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"102"
		"mode"		"dpad"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button MIDDLE"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"103"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"104"
		"mode"		"trigger"
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F"
						}
						"settings"
						{
							"hold_repeats"		"1"
							"repeat_rate"		"51"
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"105"
		"mode"		"absolute_mouse"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"106"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"107"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"108"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"109"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"110"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"111"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"112"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"113"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"114"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"115"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"116"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"117"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"118"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"119"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"120"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"121"
		"mode"		"scrollwheel"
		"description"		""
		"inputs"
		{
			"scroll_clockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_DOWN"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"scroll_counterclockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_UP"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
			"scroll_angle"		"136"
			"scroll_type"		"2"
		}
	}
	"group"
	{
		"id"		"122"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"123"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"128"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"130"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"131"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"132"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"133"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"134"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"135"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"136"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"137"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"138"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"139"
		"mode"		"disabled"
		"description"		""
	}
	"group"
	{
		"id"		"7"
		"mode"		"switches"
		"description"		"#Description"
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press ESCAPE"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press Q"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press E"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_left"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_right"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"controller_action hold_layer 4 0 0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mode_shift left_trackpad 15"
						}
						"settings"
						{
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mode_shift right_trackpad 85"
						}
						"settings"
						{
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"16"
		"mode"		"switches"
		"description"		""
		"inputs"
		{
			"button_menu"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press L"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_left"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE"
						}
						"settings"
						{
							"hold_repeats"		"1"
							"repeat_rate"		"51"
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"58"
		"mode"		"switches"
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"97"
		"mode"		"switches"
		"description"		""
		"inputs"
		{
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press T"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_left"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE"
						}
						"settings"
						{
							"hold_repeats"		"1"
							"repeat_rate"		"51"
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"140"
		"mode"		"disabled"
		"description"		""
	}
	"preset"
	{
		"id"		"0"
		"name"		"Default"
		"group_source_bindings"
		{
			"7"		"switch active"
			"1"		"left_trackpad inactive"
			"14"		"left_trackpad active"
			"15"		"left_trackpad inactive modeshift"
			"93"		"left_trackpad inactive"
			"2"		"right_trackpad inactive"
			"6"		"right_trackpad inactive"
			"13"		"right_trackpad active"
			"85"		"right_trackpad active modeshift"
			"0"		"button_diamond active"
			"3"		"joystick inactive"
			"11"		"joystick active"
			"4"		"left_trigger active"
			"5"		"right_trigger active"
			"8"		"gyro inactive"
			"10"		"gyro active"
		}
	}
	"preset"
	{
		"id"		"1"
		"name"		"Preset_1000001"
		"group_source_bindings"
		{
			"16"		"switch active"
			"18"		"left_trackpad inactive"
			"25"		"left_trackpad active"
			"19"		"right_trackpad active"
			"17"		"button_diamond active"
			"20"		"joystick active"
			"21"		"left_trigger active"
			"22"		"right_trigger active"
			"23"		"gyro active"
		}
	}
	"preset"
	{
		"id"		"2"
		"name"		"Preset_1000002"
		"group_source_bindings"
		{
			"58"		"switch active"
			"59"		"left_trackpad active"
			"61"		"right_trackpad active"
			"60"		"button_diamond active"
			"62"		"joystick active"
			"63"		"left_trigger active"
			"64"		"right_trigger active"
			"65"		"gyro active"
		}
	}
	"preset"
	{
		"id"		"3"
		"name"		"Preset_1000003"
		"group_source_bindings"
		{
			"97"		"switch active"
			"98"		"left_trackpad inactive"
			"121"		"left_trackpad active"
			"99"		"right_trackpad active"
			"100"		"right_trackpad active modeshift"
			"101"		"button_diamond active"
			"102"		"joystick active"
			"103"		"left_trigger active"
			"104"		"right_trigger active"
			"105"		"gyro active"
		}
	}
	"settings"
	{
		"left_trackpad_mode"		"0"
		"right_trackpad_mode"		"0"
		"action_set_trigger_cursor_show"		"0"
		"action_set_trigger_cursor_hide"		"0"
	}
}
