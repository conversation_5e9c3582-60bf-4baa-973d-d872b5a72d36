"controller_mappings"
{
	"version"		"3"
	"revision"		"59"
	"title"		"#Title"
	"description"		"#SettingsController_AutosaveDescription"
	"creator"		"76561198314857930"
	"progenitor"		"template://wasd.vdf"
	"url"		"autosave://C:\\Steam\\steamapps\\common\\Steam Controller Configs\\354592202\\config\\230410\\controller_steamcontroller_gordon.vdf"
	"export_type"		"unknown"
	"controller_type"		"controller_steamcontroller_gordon"
	"controller_caps"		"2179061"
	"major_revision"		"0"
	"minor_revision"		"0"
	"Timestamp"		"1697477648"
	"actions"
	{
		"Default"
		{
			"title"		"Default"
			"legacy_set"		"1"
		}
	}
	"action_layers"
	{
		"Preset_1000001"
		{
			"title"		"Abilities"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
	}
	"localization"
	{
		"english"
		{
			"Action_AccelY"		"Ascend"
			"Action_AccelYInvert"		"Descend"
			"Action_ActivateAbility0"		"Ability A"
			"Action_ActivateAbility1"		"Ability B"
			"Action_ActivateAbility2"		"Ability C"
			"Action_ActivateAbility3"		"Ability D"
			"Action_ActivateAbility4"		"Ability E"
			"Action_AimWeapon"		"Aim weapon"
			"Action_ArchwingAscend"		"Move up (Archwing/Decoration)"
			"Action_ArchwingDescend"		"Move down (Archwing/Decoration)"
			"Action_CapturaAdvanceTime"		"Advance time"
			"Action_CapturaExit"		"Exit free camera"
			"Action_CapturaPanelVisibility"		"Toggle panel visibility"
			"Action_CapturaToggleControl"		"Settings"
			"Action_ContextAction"		"Context action"
			"Action_Crouch"		"Crouch, slide & roll"
			"Action_DecorationAdd"		"Add new Decoration"
			"Action_DecorationEdit"		"Modify Decoration"
			"Action_DecorationExit"		"Back"
			"Action_DecorationMode"		"Advanced Mode"
			"Action_DecorationPlace"		"Place Decoration"
			"Action_DecorationPushPull"		"Push/Pull Decoration"
			"Action_DecorationReset"		"Reset"
			"Action_DecorationRotate"		"Hold to Rotate"
			"Action_DecorationRotation"		"Change Rotation Axis"
			"Action_DecorationScale"		"Hold to Scale"
			"Action_DecorationScaleDown"		"Scale Down"
			"Action_DecorationScaleUp"		"Scale Up"
			"Action_DecorationSnap"		"Snap"
			"Action_DecorationSurfaceSnap"		"Surface Snapping"
			"Action_EquipRailjackRepairTool"		"Equip Omni"
			"Action_FastMovement"		"Fast Movement"
			"Action_FighterGrab"		"Grab"
			"Action_FighterMoveDown"		"Crouch"
			"Action_FighterMoveLeft"		"Move Left"
			"Action_FighterMoveRight"		"Move Right"
			"Action_FighterMoveUp"		"Jump"
			"Action_FighterParry"		"Block"
			"Action_FighterPower"		"Special"
			"Action_FighterSwing"		"Light Attack"
			"Action_FighterSwingHeavy"		"Heavy Attack"
			"Action_FighterSwingMedium"		"Medium Attack"
			"Action_FireWeapon"		"Fire weapon"
			"Action_HidePauseMenu"		"Hide pause menu"
			"Action_Inspect"		"Inspect"
			"Action_Jump"		"Jump"
			"Action_LookXY"		"Look"
			"Action_MeleeChannel"		"Melee channel"
			"Action_MenuBack"		"Back"
			"Action_MenuDown"		"Down"
			"Action_MenuGeneric1"		"Action 1"
			"Action_MenuGeneric2"		"Action 2"
			"Action_MenuGeneric3"		"Action 3"
			"Action_MenuGeneric4"		"Action 4"
			"Action_MenuGeneric5"		"Action 5"
			"Action_MenuLThumb"		"Left Stick Click"
			"Action_MenuLeft"		"Left"
			"Action_MenuLookXY"		"Secondary move"
			"Action_MenuMoveXY"		"Move"
			"Action_MenuNextMenu"		"Next menu"
			"Action_MenuPreviousMenu"		"Previous menu"
			"Action_MenuRThumb"		"Right Stick Click"
			"Action_MenuRight"		"Right"
			"Action_MenuSelect"		"Select"
			"Action_MenuToggleChat"		"Chat"
			"Action_MenuUp"		"Up"
			"Action_MoveXZ"		"Move"
			"Action_NextPower"		"Next ability"
			"Action_PlaceMarker"		"Place marker"
			"Action_PowerMenuAbility0"		"Ability A (Ability menu)"
			"Action_PowerMenuAbility1"		"Ability B (Ability menu)"
			"Action_PowerMenuAbility2"		"Ability C (Ability menu)"
			"Action_PowerMenuAbility3"		"Ability D (Ability menu)"
			"Action_PowerMenuAbility4"		"Ability E (Ability menu)"
			"Action_PreviousPower"		"Previous ability"
			"Action_QuickMelee"		"Quick melee"
			"Action_Reload"		"Reload"
			"Action_ReverseCamera"		"Reverse camera"
			"Action_ScoopChannel"		"Throw"
			"Action_ScoopParry"		"Ball Magnet"
			"Action_ScoopPass"		"Pass"
			"Action_ScoopSwing"		"Check"
			"Action_SecondaryFire"		"Secondary fire"
			"Action_ShawzinChangeInstrument"		"Change Instrument"
			"Action_ShawzinChangeScale"		"Change Scale"
			"Action_ShawzinExit"		"Exit"
			"Action_ShawzinFretAltOne"		"Sky Fret Alt"
			"Action_ShawzinFretAltThree"		"Water Fret Alt"
			"Action_ShawzinFretAltTwo"		"Earth Fret Alt"
			"Action_ShawzinFretFour"		"Fret Four"
			"Action_ShawzinFretOne"		"Sky Fret"
			"Action_ShawzinFretThree"		"Water Fret"
			"Action_ShawzinFretTwo"		"Earth Fret"
			"Action_ShawzinNoteOne"		"Note One"
			"Action_ShawzinNoteThree"		"Note Three"
			"Action_ShawzinNoteTwo"		"Note Two"
			"Action_ShawzinNoteWhammy"		"Note Whammy"
			"Action_ShawzinSongList"		"Song List"
			"Action_ShawzinStepSequencer"		"Step Sequencer"
			"Action_ShowConsumablesMenu"		"Item popup"
			"Action_ShowLevelMap"		"Show level map"
			"Action_ShowPauseMenu"		"Show pause menu"
			"Action_ShowPowerMenu"		"Ability menu"
			"Action_SlowMovement"		"Slow Movement"
			"Action_Sprint"		"Sprint"
			"Action_SwitchWeapon"		"Switch weapon"
			"Action_TiltYaw"		"Tilt right"
			"Action_TiltYawInvert"		"Tilt left"
			"Action_ToggleFreeCamera"		"Toggle free camera"
			"Action_UseGearItem0"		"Gear hotkey 1"
			"Action_UseGearItem1"		"Gear hotkey 2"
			"Action_UseGearItem2"		"Gear hotkey 3"
			"Action_UseGearItem3"		"Gear hotkey 4"
			"Action_UseGearItem4"		"Gear hotkey 5"
			"Action_UseGearItem5"		"Gear hotkey 6"
			"Action_UseGearItem6"		"Gear hotkey 7"
			"Action_UseGearItem7"		"Gear hotkey 8"
			"Action_UsePower"		"Use selected ability"
			"Action_ViewHumanPlayers"		"Show player list"
			"Action_ViewRailjackSystems"		"View Railjack Systems"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"Decoration"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Launcher"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menu"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Game"
			"Set_WarframeMelee"		"Melee Mode"
		}
		"french"
		{
			"Action_AccelY"		"Monter"
			"Action_AccelYInvert"		"Descendre"
			"Action_ActivateAbility0"		"Pouvoir A"
			"Action_ActivateAbility1"		"Pouvoir B"
			"Action_ActivateAbility2"		"Pouvoir C"
			"Action_ActivateAbility3"		"Pouvoir D"
			"Action_ActivateAbility4"		"Pouvoir E"
			"Action_AimWeapon"		"Viser"
			"Action_ArchwingAscend"		"Monter (Archwing/Décoration)"
			"Action_ArchwingDescend"		"Descendre (Archwing/Décoration)"
			"Action_CapturaAdvanceTime"		"Avancer le temps"
			"Action_CapturaExit"		"Quitter la caméra libre"
			"Action_CapturaPanelVisibility"		"Activer la visibilité du panneau"
			"Action_CapturaToggleControl"		"Paramètres"
			"Action_ContextAction"		"Action contextuelle"
			"Action_Crouch"		"S'accroupir, glisser et roulade"
			"Action_DecorationAdd"		"Ajouter une nouvelle décoration"
			"Action_DecorationEdit"		"Modifier la décoration"
			"Action_DecorationExit"		"Retour"
			"Action_DecorationMode"		"Mode Avancé"
			"Action_DecorationPlace"		"Placer la décoration"
			"Action_DecorationPushPull"		"Pousser/tirer la décoration"
			"Action_DecorationReset"		"Réinitialiser"
			"Action_DecorationRotate"		"Maintenir pour faire pivoter"
			"Action_DecorationRotation"		"Changer l'axe de rotation"
			"Action_DecorationScale"		"Maintenir pour ajuster la taille"
			"Action_DecorationScaleDown"		"Réduire la taille"
			"Action_DecorationScaleUp"		"Augmenter la taille"
			"Action_DecorationSnap"		"Ajuster"
			"Action_DecorationSurfaceSnap"		"Ajustement de surface"
			"Action_EquipRailjackRepairTool"		"Équiper Omni"
			"Action_FastMovement"		"Mouvement rapide"
			"Action_FighterGrab"		"Empoigner"
			"Action_FighterMoveDown"		"S'accroupir"
			"Action_FighterMoveLeft"		"Aller à gauche"
			"Action_FighterMoveRight"		"Aller à droite"
			"Action_FighterMoveUp"		"Sauter"
			"Action_FighterParry"		"Bloquer"
			"Action_FighterPower"		"Attaque Spéciale"
			"Action_FighterSwing"		"Attaque légère"
			"Action_FighterSwingHeavy"		"Attaque lourde"
			"Action_FighterSwingMedium"		"Attaque moyenne"
			"Action_FireWeapon"		"Tirer"
			"Action_HidePauseMenu"		"Masquer le menu de pause"
			"Action_Inspect"		"Inspecter"
			"Action_Jump"		"Sauter"
			"Action_LookXY"		"Regarder"
			"Action_MeleeChannel"		"Canaliser Mêlée"
			"Action_MenuBack"		"Retour"
			"Action_MenuDown"		"Bas"
			"Action_MenuGeneric1"		"Action 1"
			"Action_MenuGeneric2"		"Action 2"
			"Action_MenuGeneric3"		"Action 3"
			"Action_MenuGeneric4"		"Action 4"
			"Action_MenuGeneric5"		"Action 5"
			"Action_MenuLThumb"		"Appuyez sur le joystick gauche"
			"Action_MenuLeft"		"Gauche"
			"Action_MenuLookXY"		"Mouvement alternatif"
			"Action_MenuMoveXY"		"Mouvement"
			"Action_MenuNextMenu"		"Menu suivant"
			"Action_MenuPreviousMenu"		"Menu précédent"
			"Action_MenuRThumb"		"Appuyez sur le joystick droit"
			"Action_MenuRight"		"Droite"
			"Action_MenuSelect"		"Sélectionner"
			"Action_MenuToggleChat"		"Tchat"
			"Action_MenuUp"		"Haut"
			"Action_MoveXZ"		"Mouvement"
			"Action_NextPower"		"Pouvoir suivant"
			"Action_PlaceMarker"		"Marqueur de lieu"
			"Action_PowerMenuAbility0"		"Pouvoir A (Menu des Pouvoirs)"
			"Action_PowerMenuAbility1"		"Pouvoir B (Menu des Pouvoirs)"
			"Action_PowerMenuAbility2"		"Pouvoir C (Menu des Pouvoirs)"
			"Action_PowerMenuAbility3"		"Pouvoir D (Menu des Pouvoirs)"
			"Action_PowerMenuAbility4"		"Pouvoir E (Menu des Pouvoirs)"
			"Action_PreviousPower"		"Pouvoir précédent"
			"Action_QuickMelee"		"Mêlée rapide"
			"Action_Reload"		"Recharger"
			"Action_ReverseCamera"		"Inverser la caméra"
			"Action_ScoopChannel"		"Lancer"
			"Action_ScoopParry"		"Aimant"
			"Action_ScoopPass"		"Passer"
			"Action_ScoopSwing"		"Tacle"
			"Action_SecondaryFire"		"Tir Secondaire"
			"Action_ShawzinChangeInstrument"		"Changer d'instrument"
			"Action_ShawzinChangeScale"		"Changer d'échelle"
			"Action_ShawzinExit"		"Sortir"
			"Action_ShawzinFretAltOne"		"Frette Céleste - Alternative"
			"Action_ShawzinFretAltThree"		"Frette Aquatique - Alternative"
			"Action_ShawzinFretAltTwo"		"Frette Terrestre - Alternative"
			"Action_ShawzinFretFour"		"Frette quatre"
			"Action_ShawzinFretOne"		"Frette Céleste"
			"Action_ShawzinFretThree"		"Frette Aquatique"
			"Action_ShawzinFretTwo"		"Frette Terrestre"
			"Action_ShawzinNoteOne"		"Note 1"
			"Action_ShawzinNoteThree"		"Note 3"
			"Action_ShawzinNoteTwo"		"Note 2"
			"Action_ShawzinNoteWhammy"		"Note Whammy"
			"Action_ShawzinSongList"		"Liste des morceaux"
			"Action_ShawzinStepSequencer"		"Séquenceur"
			"Action_ShowConsumablesMenu"		"Fenêtre contextuelle de l'article"
			"Action_ShowLevelMap"		"Afficher la carte du niveau"
			"Action_ShowPauseMenu"		"Afficher le menu de pause"
			"Action_ShowPowerMenu"		"Menu des Pouvoirs"
			"Action_SlowMovement"		"Mouvement lent"
			"Action_Sprint"		"Sprint"
			"Action_SwitchWeapon"		"Changer d'arme"
			"Action_TiltYaw"		"Incliner à droite"
			"Action_TiltYawInvert"		"Incliner à gauche"
			"Action_ToggleFreeCamera"		"Activer la caméra libre"
			"Action_UseGearItem0"		"Raccourci Matériel 1"
			"Action_UseGearItem1"		"Raccourci Matériel 2"
			"Action_UseGearItem2"		"Raccourci Matériel 3"
			"Action_UseGearItem3"		"Raccourci Matériel 4"
			"Action_UseGearItem4"		"Raccourci Matériel 5"
			"Action_UseGearItem5"		"Raccourci Matériel 6"
			"Action_UseGearItem6"		"Raccourci Matériel 7"
			"Action_UseGearItem7"		"Raccourci Matériel 8"
			"Action_UsePower"		"Utiliser le Pouvoir sélectionné"
			"Action_ViewHumanPlayers"		"Afficher la liste des joueurs"
			"Action_ViewRailjackSystems"		"Voir les Systèmes Railjack"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"Décoration"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Lanceur"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menu"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Jeu"
			"Set_WarframeMelee"		"Mode Mêlée"
		}
		"italian"
		{
			"Action_AccelY"		"Sali"
			"Action_AccelYInvert"		"Scendi"
			"Action_ActivateAbility0"		"Abilità A"
			"Action_ActivateAbility1"		"Abilità B"
			"Action_ActivateAbility2"		"Abilità C"
			"Action_ActivateAbility3"		"Abilità D"
			"Action_ActivateAbility4"		"Abilità E"
			"Action_AimWeapon"		"Mirare"
			"Action_ArchwingAscend"		"Muovi verso l'alto (Archwing/Decorazione)"
			"Action_ArchwingDescend"		"Muovi verso il basso (Archwing/Decorazione)"
			"Action_CapturaAdvanceTime"		"Avanza tempo"
			"Action_CapturaExit"		"Esci dalla telecamera libera"
			"Action_CapturaPanelVisibility"		"Mostra/Nascondi pannello"
			"Action_CapturaToggleControl"		"Impostazioni"
			"Action_ContextAction"		"Contesto Azione"
			"Action_Crouch"		"Abbassati, scivola e rotola"
			"Action_DecorationAdd"		"Aggiungi nuove Decorazioni"
			"Action_DecorationEdit"		"Modifica Decorazione"
			"Action_DecorationExit"		"Indietro"
			"Action_DecorationMode"		"Modalità Avanzata"
			"Action_DecorationPlace"		"Piazza Decorazione"
			"Action_DecorationPushPull"		"Allontana/Avvicina Decorazione"
			"Action_DecorationReset"		"Ripristina"
			"Action_DecorationRotate"		"Tieni Premuto per Ruotare"
			"Action_DecorationRotation"		"Cambia Assi di Rotazione"
			"Action_DecorationScale"		"Tieni Premuto per Scalare"
			"Action_DecorationScaleDown"		"Rimpicciolisci"
			"Action_DecorationScaleUp"		"Ingrandisci"
			"Action_DecorationSnap"		"Aggancia"
			"Action_DecorationSurfaceSnap"		"Snap a Superficie"
			"Action_EquipRailjackRepairTool"		"Equipaggia l'Omni"
			"Action_FastMovement"		"Movimento Rapido"
			"Action_FighterGrab"		"Afferra"
			"Action_FighterMoveDown"		"Accovacciati"
			"Action_FighterMoveLeft"		"Sinistra"
			"Action_FighterMoveRight"		"Destra"
			"Action_FighterMoveUp"		"Salta"
			"Action_FighterParry"		"Blocca"
			"Action_FighterPower"		"Speciale"
			"Action_FighterSwing"		"Attacco Leggero"
			"Action_FighterSwingHeavy"		"Attacco Pesante"
			"Action_FighterSwingMedium"		"Attacco Medio"
			"Action_FireWeapon"		"Sparare"
			"Action_HidePauseMenu"		"Nascondi il menu di pausa"
			"Action_Inspect"		"Ispeziona"
			"Action_Jump"		"Salta"
			"Action_LookXY"		"Guarda"
			"Action_MeleeChannel"		"Corpo a Corpo Canalizzato"
			"Action_MenuBack"		"Indietro"
			"Action_MenuDown"		"Giù"
			"Action_MenuGeneric1"		"Azione 1"
			"Action_MenuGeneric2"		"Azione 2"
			"Action_MenuGeneric3"		"Azione 3"
			"Action_MenuGeneric4"		"Azione 4"
			"Action_MenuGeneric5"		"Azione 5"
			"Action_MenuLThumb"		"Clic con la Levetta Sinistra"
			"Action_MenuLeft"		"Sinistra"
			"Action_MenuLookXY"		"Movimento secondario"
			"Action_MenuMoveXY"		"Muovi"
			"Action_MenuNextMenu"		"Prossimo menu"
			"Action_MenuPreviousMenu"		"Menu precedente"
			"Action_MenuRThumb"		"Click con la Levetta Destra"
			"Action_MenuRight"		"Destra"
			"Action_MenuSelect"		"Seleziona"
			"Action_MenuToggleChat"		"Chat"
			"Action_MenuUp"		"Su"
			"Action_MoveXZ"		"Muovi"
			"Action_NextPower"		"Prossima Abilità"
			"Action_PlaceMarker"		"Segnaposto"
			"Action_PlayEmote0"		" "
			"Action_PlayEmote1"		" "
			"Action_PlayEmote10"		" "
			"Action_PlayEmote11"		" "
			"Action_PlayEmote2"		" "
			"Action_PlayEmote3"		" "
			"Action_PlayEmote4"		" "
			"Action_PlayEmote5"		" "
			"Action_PlayEmote6"		" "
			"Action_PlayEmote7"		" "
			"Action_PlayEmote8"		" "
			"Action_PlayEmote9"		" "
			"Action_PowerMenuAbility0"		"Abilità A (Menu Abilità)"
			"Action_PowerMenuAbility1"		"Abilità B (Menu Abilità)"
			"Action_PowerMenuAbility2"		"Abilità C (Menu Abilità)"
			"Action_PowerMenuAbility3"		"Abilità D (Menu Abilità)"
			"Action_PowerMenuAbility4"		"Abilità E (Menu Abilità)"
			"Action_PreviousPower"		"Abilità precedente"
			"Action_QuickMelee"		"Corpo a Corpo Rapido"
			"Action_Reload"		"Ricarica"
			"Action_ReverseCamera"		"Camera inversa"
			"Action_ScoopChannel"		"Lancia"
			"Action_ScoopParry"		"Attira Palla"
			"Action_ScoopPass"		"Passaggio"
			"Action_ScoopSwing"		"Placca"
			"Action_SecondaryFire"		"Fuoco secondario"
			"Action_ShawzinChangeInstrument"		"Cambia Strumento"
			"Action_ShawzinChangeScale"		"Cambia Scala"
			"Action_ShawzinExit"		"Esci"
			"Action_ShawzinFretAltOne"		"Tasto Cielo - Alternativo"
			"Action_ShawzinFretAltThree"		"Tasto Acqua - Alternativo"
			"Action_ShawzinFretAltTwo"		"Tasto Terra - Alternativo"
			"Action_ShawzinFretFour"		"Tasto Quattro"
			"Action_ShawzinFretOne"		"Tasto Cielo"
			"Action_ShawzinFretThree"		"Tasto Acqua"
			"Action_ShawzinFretTwo"		"Tasto Terra"
			"Action_ShawzinNoteOne"		"Nota Uno"
			"Action_ShawzinNoteThree"		"Nota Tre"
			"Action_ShawzinNoteTwo"		"Nota Due"
			"Action_ShawzinNoteWhammy"		"Nota Whammy"
			"Action_ShawzinSongList"		"Lista delle Canzoni"
			"Action_ShawzinStepSequencer"		"Sequenziatore di Passi"
			"Action_ShowConsumablesMenu"		"Popup oggetto"
			"Action_ShowLevelMap"		"Mostra Mappa del Livello"
			"Action_ShowPauseMenu"		"Mostra il menu di pausa"
			"Action_ShowPowerMenu"		"Menu Abilità"
			"Action_SlowMovement"		"Rallenta Movimento"
			"Action_Sprint"		"Scatta"
			"Action_SwitchWeapon"		"Cambia arma"
			"Action_TiltYaw"		"Inclina a destra"
			"Action_TiltYawInvert"		"Inclina a sinistra"
			"Action_ToggleFreeCamera"		"Abilita telecamera libera"
			"Action_UseGearItem0"		"Tasto Rapido Dotazione 1"
			"Action_UseGearItem1"		"Tasto Rapido Dotazione 2"
			"Action_UseGearItem10"		" "
			"Action_UseGearItem11"		" "
			"Action_UseGearItem2"		"Tasto Rapido Dotazione 3"
			"Action_UseGearItem3"		"Tasto Rapido Dotazione 4"
			"Action_UseGearItem4"		"Tasto Rapido Dotazione 5"
			"Action_UseGearItem5"		"Tasto Rapido Dotazione 6"
			"Action_UseGearItem6"		"Tasto Rapido Dotazione 7"
			"Action_UseGearItem7"		"Tasto Rapido Dotazione 8"
			"Action_UseGearItem8"		" "
			"Action_UseGearItem9"		" "
			"Action_UsePower"		"Utilizza l'Abilità Selezionata"
			"Action_ViewHumanPlayers"		"Mostra elenco giocatori"
			"Action_ViewRailjackSystems"		"Visualizza i Sistemi Railjack"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"Decorazione"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Launcher"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menu"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Gioco"
			"Set_WarframeMelee"		"Modalità Corpo a Corpo"
		}
		"german"
		{
			"Action_AccelY"		"Hoch"
			"Action_AccelYInvert"		"Runter"
			"Action_ActivateAbility0"		"Fähigkeit A"
			"Action_ActivateAbility1"		"Fähigkeit B"
			"Action_ActivateAbility2"		"Fähigkeit C"
			"Action_ActivateAbility3"		"Fähigkeit D"
			"Action_ActivateAbility4"		"Fähigkeit E"
			"Action_AimWeapon"		"Mit Waffe zielen"
			"Action_ArchwingAscend"		"Hochbewegen (Archwing/Dekoration)"
			"Action_ArchwingDescend"		"Runterbewegen (Archwing/Dekoration)"
			"Action_CapturaAdvanceTime"		"Zeit fortsetzen"
			"Action_CapturaExit"		"Freie Kamera verlassen"
			"Action_CapturaPanelVisibility"		"Feldanzeige umschalten"
			"Action_CapturaToggleControl"		"Einstellungen"
			"Action_ContextAction"		"Kontextaktion"
			"Action_Crouch"		"Ducken, Rutschen & Rollen"
			"Action_DecorationAdd"		"Neue Dekoration hinzufügen"
			"Action_DecorationEdit"		"Dekoration ändern"
			"Action_DecorationExit"		"Zurück"
			"Action_DecorationMode"		"Fortgeschrittener Modus"
			"Action_DecorationPlace"		"Dekoration platzieren"
			"Action_DecorationPushPull"		"Dekoration schieben/ziehen"
			"Action_DecorationReset"		"Zurücksetzen"
			"Action_DecorationRotate"		"Zum Drehen gedrückt halten"
			"Action_DecorationRotation"		"Rotationsachse ändern"
			"Action_DecorationScale"		"Zum Skalieren gedrückt halten"
			"Action_DecorationScaleDown"		"Runterskalieren"
			"Action_DecorationScaleUp"		"Hochskalieren"
			"Action_DecorationSnap"		"Einrasten"
			"Action_DecorationSurfaceSnap"		"Oberflächen Einrastung"
			"Action_EquipRailjackRepairTool"		"Omni ausrüsten"
			"Action_FastMovement"		"Schnelle Bewegung"
			"Action_FighterGrab"		"Greifen"
			"Action_FighterMoveDown"		"Ducken"
			"Action_FighterMoveLeft"		"Nach links bewegen"
			"Action_FighterMoveRight"		"Nach rechts bewegen"
			"Action_FighterMoveUp"		"Springen"
			"Action_FighterParry"		"Blocken"
			"Action_FighterPower"		"Spezial"
			"Action_FighterSwing"		"Leichter Angriff"
			"Action_FighterSwingHeavy"		"Schwerer Angriff"
			"Action_FighterSwingMedium"		"Mittlerer Angriff"
			"Action_FireWeapon"		"Waffe abfeuern"
			"Action_HidePauseMenu"		"Pausenmenü ausblenden"
			"Action_Inspect"		"Untersuchen"
			"Action_Jump"		"Springen"
			"Action_LookXY"		"Umsehen"
			"Action_MeleeChannel"		"Nahkampf kanalisieren"
			"Action_MenuBack"		"Zurück"
			"Action_MenuDown"		"Runter"
			"Action_MenuGeneric1"		"Aktion 1"
			"Action_MenuGeneric2"		"Aktion 2"
			"Action_MenuGeneric3"		"Aktion 3"
			"Action_MenuGeneric4"		"Aktion 4"
			"Action_MenuGeneric5"		"Aktion 5"
			"Action_MenuLThumb"		"Linken Stick drücken"
			"Action_MenuLeft"		"Links"
			"Action_MenuLookXY"		"Sekundäre Bewegung"
			"Action_MenuMoveXY"		"Bewegung"
			"Action_MenuNextMenu"		"Nächstes Menü"
			"Action_MenuPreviousMenu"		"Vorheriges Menü"
			"Action_MenuRThumb"		"Rechten Stick drücken"
			"Action_MenuRight"		"Rechts"
			"Action_MenuSelect"		"Wählen"
			"Action_MenuToggleChat"		"Chat"
			"Action_MenuUp"		"Hoch"
			"Action_MoveXZ"		"Bewegen"
			"Action_NextPower"		"Nächste Fähigkeit"
			"Action_PlaceMarker"		"Markierung setzen"
			"Action_PowerMenuAbility0"		"Fähigkeit A (Fähigkeitsmenü)"
			"Action_PowerMenuAbility1"		"Fähigkeit B (Fähigkeitsmenü)"
			"Action_PowerMenuAbility2"		"Fähigkeit C (Fähigkeitsmenü)"
			"Action_PowerMenuAbility3"		"Fähigkeit D (Fähigkeitsmenü)"
			"Action_PowerMenuAbility4"		"Fähigkeit E (Fähigkeitsmenü)"
			"Action_PreviousPower"		"Vorherige Fähigkeit"
			"Action_QuickMelee"		"Schneller Nahkampf"
			"Action_Reload"		"Nachladen"
			"Action_ReverseCamera"		"Kamera umkehren"
			"Action_ScoopChannel"		"Werfen"
			"Action_ScoopParry"		"Ball-Magnet"
			"Action_ScoopPass"		"Passen"
			"Action_ScoopSwing"		"Checken"
			"Action_SecondaryFire"		"Sekundärfeuer"
			"Action_ShawzinChangeInstrument"		"Instrument ändern"
			"Action_ShawzinChangeScale"		"Maßstab ändern"
			"Action_ShawzinExit"		"Verlassen"
			"Action_ShawzinFretAltOne"		"Alt. Bund: Himmel"
			"Action_ShawzinFretAltThree"		"Alt. Bund: Wasser"
			"Action_ShawzinFretAltTwo"		"Alt. Bund: Erde"
			"Action_ShawzinFretFour"		"Bund vier"
			"Action_ShawzinFretOne"		"Bund: Himmel"
			"Action_ShawzinFretThree"		"Bund: Wasser"
			"Action_ShawzinFretTwo"		"Bund: Erde"
			"Action_ShawzinNoteOne"		"Erste Note"
			"Action_ShawzinNoteThree"		"Dritte Note"
			"Action_ShawzinNoteTwo"		"Zweite Note"
			"Action_ShawzinNoteWhammy"		"Noten-Whammy"
			"Action_ShawzinSongList"		"Songliste"
			"Action_ShawzinStepSequencer"		"Stufen-Sequenzer"
			"Action_ShowConsumablesMenu"		"Gegenstands-Popup"
			"Action_ShowLevelMap"		"Levelkarte anzeigen"
			"Action_ShowPauseMenu"		"Pausenmenü anzeigen"
			"Action_ShowPowerMenu"		"Fähigkeitsmenü"
			"Action_SlowMovement"		"Langsame Bewegung"
			"Action_Sprint"		"Sprinten"
			"Action_SwitchWeapon"		"Waffe wechseln"
			"Action_TiltYaw"		"Nach rechts neigen"
			"Action_TiltYawInvert"		"Nach links neigen"
			"Action_ToggleFreeCamera"		"Freie Kamera umschalten"
			"Action_UseGearItem0"		"Ausrüstung-Hotkey 1"
			"Action_UseGearItem1"		"Ausrüstung-Hotkey 2"
			"Action_UseGearItem2"		"Ausrüstung-Hotkey 3"
			"Action_UseGearItem3"		"Ausrüstung-Hotkey 4"
			"Action_UseGearItem4"		"Ausrüstung-Hotkey 5"
			"Action_UseGearItem5"		"Ausrüstung-Hotkey 6"
			"Action_UseGearItem6"		"Ausrüstung-Hotkey 7"
			"Action_UseGearItem7"		"Ausrüstung-Hotkey 8"
			"Action_UsePower"		"Ausgewählte Fähigkeit nutzen"
			"Action_ViewHumanPlayers"		"Spielerliste anzeigen"
			"Action_ViewRailjackSystems"		"Railjack-Systeme ansehen"
			"Set_Captura"		"Photora"
			"Set_Decoration"		"Dekoration"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Launcher"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menü"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Spiel"
			"Set_WarframeMelee"		"Nahkampfmodus"
		}
		"spanish"
		{
			"Action_AccelY"		"Ascender"
			"Action_AccelYInvert"		"Descender"
			"Action_ActivateAbility0"		"Habilidad A"
			"Action_ActivateAbility1"		"Habilidad B"
			"Action_ActivateAbility2"		"Habilidad C"
			"Action_ActivateAbility3"		"Habilidad D"
			"Action_ActivateAbility4"		"Habilidad E"
			"Action_AimWeapon"		"Apuntar"
			"Action_ArchwingAscend"		"Subir (Archwing/Decoración)"
			"Action_ArchwingDescend"		"Bajar (Archwing/Decoración)"
			"Action_CapturaAdvanceTime"		"Adelantar el tiempo"
			"Action_CapturaExit"		"Salir de la cámara libre"
			"Action_CapturaPanelVisibility"		"Activar la visibilidad del panel"
			"Action_CapturaToggleControl"		"Configuraciones"
			"Action_ContextAction"		"Acción de contexto"
			"Action_Crouch"		"Agacharse, deslizarse y rodar"
			"Action_DecorationAdd"		"Agregar nueva decoración"
			"Action_DecorationEdit"		"Modificar decoración"
			"Action_DecorationExit"		"Atrás"
			"Action_DecorationMode"		"Modo avanzado"
			"Action_DecorationPlace"		"Colocar decoración"
			"Action_DecorationPushPull"		"Acercar/Alejar decoración"
			"Action_DecorationReset"		"Reiniciar"
			"Action_DecorationRotate"		"Mantener para rotar"
			"Action_DecorationRotation"		"Cambiar eje de rotación"
			"Action_DecorationScale"		"Mantener para escalar"
			"Action_DecorationScaleDown"		"Reducir"
			"Action_DecorationScaleUp"		"Ampliar"
			"Action_DecorationSnap"		"Ajustar"
			"Action_DecorationSurfaceSnap"		"Ajustar a la superficie"
			"Action_EquipRailjackRepairTool"		"Equipar Omni"
			"Action_FastMovement"		"Movimiento rápido"
			"Action_FighterGrab"		"Agarrar"
			"Action_FighterMoveDown"		"Agacharse"
			"Action_FighterMoveLeft"		"Moverse a la izquierda"
			"Action_FighterMoveRight"		"Moverse a la derecha"
			"Action_FighterMoveUp"		"Saltar"
			"Action_FighterParry"		"Bloquear"
			"Action_FighterPower"		"Especial"
			"Action_FighterSwing"		"Ataque ligero"
			"Action_FighterSwingHeavy"		"Ataque pesado"
			"Action_FighterSwingMedium"		"Ataque medio"
			"Action_FireWeapon"		"Disparar"
			"Action_HidePauseMenu"		"Ocultar el menú de pausa"
			"Action_Inspect"		"Inspeccionar"
			"Action_Jump"		"Saltar"
			"Action_LookXY"		"Ver"
			"Action_MeleeChannel"		"Canalización cuerpo a cuerpo"
			"Action_MenuBack"		"Atrás"
			"Action_MenuDown"		"Abajo"
			"Action_MenuGeneric1"		"Acción 1"
			"Action_MenuGeneric2"		"Acción 2"
			"Action_MenuGeneric3"		"Acción 3"
			"Action_MenuGeneric4"		"Acción 4"
			"Action_MenuGeneric5"		"Acción 5"
			"Action_MenuLThumb"		"Presionar stick izquierdo"
			"Action_MenuLeft"		"Izquierda"
			"Action_MenuLookXY"		"Movimiento secundario"
			"Action_MenuMoveXY"		"Mover"
			"Action_MenuNextMenu"		"Menú siguiente"
			"Action_MenuPreviousMenu"		"Menú anterior"
			"Action_MenuRThumb"		"Presionar sitck derecho"
			"Action_MenuRight"		"Derecha"
			"Action_MenuSelect"		"Seleccionar"
			"Action_MenuToggleChat"		"Chat"
			"Action_MenuUp"		"Arriba"
			"Action_MoveXZ"		"Mover"
			"Action_NextPower"		"Siguiente habilidad"
			"Action_PlaceMarker"		"Marcador de lugar"
			"Action_PlayEmote0"		" "
			"Action_PlayEmote1"		" "
			"Action_PlayEmote10"		" "
			"Action_PlayEmote11"		" "
			"Action_PlayEmote2"		" "
			"Action_PlayEmote3"		" "
			"Action_PlayEmote4"		" "
			"Action_PlayEmote5"		" "
			"Action_PlayEmote6"		" "
			"Action_PlayEmote7"		" "
			"Action_PlayEmote8"		" "
			"Action_PlayEmote9"		" "
			"Action_PowerMenuAbility0"		"Habilidad A (Menú de habilidades)"
			"Action_PowerMenuAbility1"		"Habilidad B (Menú de habilidades)"
			"Action_PowerMenuAbility2"		"Habilidad C (Menú de habilidades)"
			"Action_PowerMenuAbility3"		"Habilidad D (Menú de habilidades)"
			"Action_PowerMenuAbility4"		"Habilidad E (Menú de habilidades)"
			"Action_PreviousPower"		"Habilidad previa"
			"Action_QuickMelee"		"Ataque rápido de cuerpo a cuerpo"
			"Action_Reload"		"Recargar"
			"Action_ReverseCamera"		"Invertir cámara"
			"Action_ScoopChannel"		"Lanzar"
			"Action_ScoopParry"		"Imán del balón"
			"Action_ScoopPass"		"Pase"
			"Action_ScoopSwing"		"Bloqueo"
			"Action_SecondaryFire"		"Disparo alternativo"
			"Action_ShawzinChangeInstrument"		"Cambiar instrumento"
			"Action_ShawzinChangeScale"		"Cambiar escala"
			"Action_ShawzinExit"		"Salir"
			"Action_ShawzinFretAltOne"		"Traste celeste alternativo"
			"Action_ShawzinFretAltThree"		"Traste acuático alternativo"
			"Action_ShawzinFretAltTwo"		"Traste terrestre alternativo"
			"Action_ShawzinFretFour"		"Traste cuatro"
			"Action_ShawzinFretOne"		"Traste celeste"
			"Action_ShawzinFretThree"		"Traste acuático"
			"Action_ShawzinFretTwo"		"Traste terrestre"
			"Action_ShawzinNoteOne"		"Primera nota"
			"Action_ShawzinNoteThree"		"Tercera nota"
			"Action_ShawzinNoteTwo"		"Segunda nota"
			"Action_ShawzinNoteWhammy"		"Whammy"
			"Action_ShawzinSongList"		"Lista de canciones"
			"Action_ShawzinStepSequencer"		"Secuenciador de pasos"
			"Action_ShowConsumablesMenu"		"Objeto emergente"
			"Action_ShowLevelMap"		"Mostrar mapa del nivel"
			"Action_ShowPauseMenu"		"Mostrar menú de pausa"
			"Action_ShowPowerMenu"		"Menú de habilidades"
			"Action_SlowMovement"		"Movimiento lento"
			"Action_Sprint"		"Correr"
			"Action_SwitchWeapon"		"Cambiar de arma"
			"Action_TiltYaw"		"Inclinar a la derecha"
			"Action_TiltYawInvert"		"Inclinar a la izquierda"
			"Action_ToggleFreeCamera"		"Activar cámara libre"
			"Action_UseGearItem0"		"Tecla rápida de herramienta 1"
			"Action_UseGearItem1"		"Tecla rápida de herramienta 2"
			"Action_UseGearItem10"		" "
			"Action_UseGearItem11"		" "
			"Action_UseGearItem2"		"Tecla rápida de herramienta 3"
			"Action_UseGearItem3"		"Tecla rápida de herramienta 4"
			"Action_UseGearItem4"		"Tecla rápida de herramienta 5"
			"Action_UseGearItem5"		"Tecla rápida de herramienta 6"
			"Action_UseGearItem6"		"Tecla rápida de herramienta 7"
			"Action_UseGearItem7"		"Tecla rápida de herramienta 8"
			"Action_UseGearItem8"		" "
			"Action_UseGearItem9"		" "
			"Action_UsePower"		"Usar habilidad seleccionada"
			"Action_ViewHumanPlayers"		"Mostrar lista de jugadores"
			"Action_ViewRailjackSystems"		"Ver sistemas del Railjack"
			"Set_Captura"		"Modo de Estudio"
			"Set_Decoration"		"Decoración"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Lanzador"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menú"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Juego"
			"Set_WarframeMelee"		"Modo cuerpo a cuerpo"
		}
		"portuguese"
		{
			"Action_AccelY"		"Subir"
			"Action_AccelYInvert"		"Descer"
			"Action_ActivateAbility0"		"Habilidade A"
			"Action_ActivateAbility1"		"Habilidade B"
			"Action_ActivateAbility2"		"Habilidade C"
			"Action_ActivateAbility3"		"Habilidade D"
			"Action_ActivateAbility4"		"Habilidade E"
			"Action_AimWeapon"		"Mirar Arma"
			"Action_ArchwingAscend"		"Mover para cima (Archwing/Decoração)"
			"Action_ArchwingDescend"		"Mover para baixo (Archwing/Decoração)"
			"Action_CapturaAdvanceTime"		"Avançar tempo"
			"Action_CapturaExit"		"Sair da Câmera Livre"
			"Action_CapturaPanelVisibility"		"Alternar Visibilidade do Painel"
			"Action_CapturaToggleControl"		"Configurações"
			"Action_ContextAction"		"Ação Contextual"
			"Action_Crouch"		"Agachar, Deslizar e Rolar"
			"Action_DecorationAdd"		"Adicionar nova Decoração"
			"Action_DecorationEdit"		"Modificar Decoração"
			"Action_DecorationExit"		"Voltar"
			"Action_DecorationMode"		"Modo Avançado"
			"Action_DecorationPlace"		"Colocar Decoração"
			"Action_DecorationPushPull"		"Puxar/Empurrar Decoração"
			"Action_DecorationReset"		"Redefinir"
			"Action_DecorationRotate"		"Segure para Girar"
			"Action_DecorationRotation"		"Alterar Eixo de Rotação"
			"Action_DecorationScale"		"Segure para Escalonar"
			"Action_DecorationScaleDown"		"Reduzir Escala"
			"Action_DecorationScaleUp"		"Aumentar Escala"
			"Action_DecorationSnap"		"Fixar"
			"Action_DecorationSurfaceSnap"		"Fixação em Superfícies"
			"Action_EquipRailjackRepairTool"		"Equipar Ferramenta Ômnica"
			"Action_FastMovement"		"Movimento Rápido"
			"Action_FighterGrab"		"Agarrão"
			"Action_FighterMoveDown"		"Agachar"
			"Action_FighterMoveLeft"		"Mover à Esquerda"
			"Action_FighterMoveRight"		"Mover à Direita"
			"Action_FighterMoveUp"		"Pular"
			"Action_FighterParry"		"Bloquear"
			"Action_FighterPower"		"Especial"
			"Action_FighterSwing"		"Ataque Leve"
			"Action_FighterSwingHeavy"		"Ataque Pesado"
			"Action_FighterSwingMedium"		"Ataque Médio"
			"Action_FireWeapon"		"Disparar Arma"
			"Action_HidePauseMenu"		"Ocultar Menu de Pausa"
			"Action_Inspect"		"Inspecionar"
			"Action_Jump"		"Pular"
			"Action_LookXY"		"Olhar"
			"Action_MeleeChannel"		"Canalização Corpo a Corpo"
			"Action_MenuBack"		"Voltar"
			"Action_MenuDown"		"Abaixo"
			"Action_MenuGeneric1"		"Ação 1"
			"Action_MenuGeneric2"		"Ação 2"
			"Action_MenuGeneric3"		"Ação 3"
			"Action_MenuGeneric4"		"Ação 4"
			"Action_MenuGeneric5"		"Ação 5"
			"Action_MenuLThumb"		"Clique do Analógico Esquerdo"
			"Action_MenuLeft"		"Esquerda"
			"Action_MenuLookXY"		"Movimento Secundário"
			"Action_MenuMoveXY"		"Mover"
			"Action_MenuNextMenu"		"Próximo Menu"
			"Action_MenuPreviousMenu"		"Menu Anterior"
			"Action_MenuRThumb"		"Clique do Analógico Direito"
			"Action_MenuRight"		"Direita"
			"Action_MenuSelect"		"Selecionar"
			"Action_MenuToggleChat"		"Bate-Papo"
			"Action_MenuUp"		"Acima"
			"Action_MoveXZ"		"Mover"
			"Action_NextPower"		"Próxima Habilidade"
			"Action_PlaceMarker"		"Posicionar Marcador"
			"Action_PlayEmote0"		" "
			"Action_PlayEmote1"		" "
			"Action_PlayEmote10"		" "
			"Action_PlayEmote11"		" "
			"Action_PlayEmote2"		" "
			"Action_PlayEmote3"		"  "
			"Action_PlayEmote4"		" "
			"Action_PlayEmote5"		" "
			"Action_PlayEmote6"		" "
			"Action_PlayEmote7"		" "
			"Action_PlayEmote8"		" "
			"Action_PlayEmote9"		" "
			"Action_PowerMenuAbility0"		"Habilidade A (Menu de Habilidade)"
			"Action_PowerMenuAbility1"		"Habilidade B (Menu de Habilidade)"
			"Action_PowerMenuAbility2"		"Habilidade C (Menu de Habilidade)"
			"Action_PowerMenuAbility3"		"Habilidade D (Menu de Habilidade)"
			"Action_PowerMenuAbility4"		"Habilidade E (Menu de Habilidade)"
			"Action_PreviousPower"		"Habilidade Anterior"
			"Action_QuickMelee"		"Corpo a Corpo Rápido"
			"Action_Reload"		"Recarregar"
			"Action_ReverseCamera"		"Câmera Reversa"
			"Action_ScoopChannel"		"Arremessar"
			"Action_ScoopParry"		"Atrair Bola"
			"Action_ScoopPass"		"Passar"
			"Action_ScoopSwing"		"Investida"
			"Action_SecondaryFire"		"Disparo Secundário"
			"Action_ShawzinChangeInstrument"		"Alterar Instrumento"
			"Action_ShawzinChangeScale"		"Alterar Escala"
			"Action_ShawzinExit"		"Sair"
			"Action_ShawzinFretAltOne"		"Traste Celestial (Alternativo)"
			"Action_ShawzinFretAltThree"		"Traste Áqueo (Alternativo)"
			"Action_ShawzinFretAltTwo"		"Traste Terral (Alternativo)"
			"Action_ShawzinFretFour"		"Quarto Traste"
			"Action_ShawzinFretOne"		"Traste Celestial"
			"Action_ShawzinFretThree"		"Traste Áqueo"
			"Action_ShawzinFretTwo"		"Traste Terral"
			"Action_ShawzinNoteOne"		"Primeira Nota"
			"Action_ShawzinNoteThree"		"Terceira Nota"
			"Action_ShawzinNoteTwo"		"Segunda Nota"
			"Action_ShawzinNoteWhammy"		"Nota Whammy"
			"Action_ShawzinSongList"		"Lista de Músicas"
			"Action_ShawzinStepSequencer"		"Sequenciador de Passos"
			"Action_ShowConsumablesMenu"		"Notificação de Item"
			"Action_ShowLevelMap"		"Exibir Mapa da Missão"
			"Action_ShowPauseMenu"		"Exibir Menu de Pausa"
			"Action_ShowPowerMenu"		"Menu de Habilidade"
			"Action_SlowMovement"		"Movimento Lento"
			"Action_Sprint"		"Correr"
			"Action_SwitchWeapon"		"Alternar Armas"
			"Action_TiltYaw"		"Inclinar à Direita"
			"Action_TiltYawInvert"		"Inclinar à Esquerda"
			"Action_ToggleFreeCamera"		"Alternar Câmera Livre"
			"Action_UseGearItem0"		"Atalho de Consumíveis 1"
			"Action_UseGearItem1"		"Atalho de Consumíveis 2"
			"Action_UseGearItem10"		" "
			"Action_UseGearItem11"		" "
			"Action_UseGearItem2"		"Atalho de Consumíveis 3"
			"Action_UseGearItem3"		"Atalho de Consumíveis 4"
			"Action_UseGearItem4"		"Atalho de Consumíveis 5"
			"Action_UseGearItem5"		"Atalho de Consumíveis 6"
			"Action_UseGearItem6"		"Atalho de Consumíveis 7"
			"Action_UseGearItem7"		"Atalho de Consumíveis 8"
			"Action_UseGearItem8"		" "
			"Action_UseGearItem9"		" "
			"Action_UsePower"		"Usar Habilidade Selecionada"
			"Action_ViewHumanPlayers"		"Exibir Lista de Jogadores"
			"Action_ViewRailjackSystems"		"Visualizar Sistemas da Railjack"
			"Set_Captura"		"Modo Captura"
			"Set_Decoration"		"Decoração"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Inicializador"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menu"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Jogo"
			"Set_WarframeMelee"		"Modo Corpo a Corpo"
		}
		"russian"
		{
			"Action_AccelY"		"Подниматься"
			"Action_AccelYInvert"		"Спускаться"
			"Action_ActivateAbility0"		"Способность A"
			"Action_ActivateAbility1"		"Способность B"
			"Action_ActivateAbility2"		"Способность C"
			"Action_ActivateAbility3"		"Способность D"
			"Action_ActivateAbility4"		"Способность E"
			"Action_AimWeapon"		"Прицелиться"
			"Action_ArchwingAscend"		"Вверх (Арчвинг/Украшение)"
			"Action_ArchwingDescend"		"Вниз (Арчвинг/Украшение)"
			"Action_CapturaAdvanceTime"		"Управление временем"
			"Action_CapturaExit"		"Выход из свободной камеры"
			"Action_CapturaPanelVisibility"		"Вкл. отображение панели"
			"Action_CapturaToggleControl"		"Настройки"
			"Action_ContextAction"		"Контекстное действие"
			"Action_Crouch"		"Присесть, скользить и перекатываться"
			"Action_DecorationAdd"		"Добавить новое Украшение"
			"Action_DecorationEdit"		"Изменить Украшение"
			"Action_DecorationExit"		"Назад"
			"Action_DecorationMode"		"Продвинутый режим"
			"Action_DecorationPlace"		"Разместить украшение"
			"Action_DecorationPushPull"		"Толкать/тянуть украшение"
			"Action_DecorationReset"		"Сброс"
			"Action_DecorationRotate"		"Удерживать для вращения"
			"Action_DecorationRotation"		"Изменить ось вращения"
			"Action_DecorationScale"		"Удерживать для вращения"
			"Action_DecorationScaleDown"		"Уменьшить"
			"Action_DecorationScaleUp"		"Увеличить"
			"Action_DecorationSnap"		"Привязать"
			"Action_DecorationSurfaceSnap"		"Привязка к поверхности"
			"Action_EquipRailjackRepairTool"		"Экипировать Омни"
			"Action_FastMovement"		"Быстрое движение"
			"Action_FighterGrab"		"Захват"
			"Action_FighterMoveDown"		"Присесть"
			"Action_FighterMoveLeft"		"Двигаться влево"
			"Action_FighterMoveRight"		"Двигаться вправо"
			"Action_FighterMoveUp"		"Прыжок"
			"Action_FighterParry"		"Блокировать"
			"Action_FighterPower"		"Особый"
			"Action_FighterSwing"		"Слабый удар"
			"Action_FighterSwingHeavy"		"Тяжелая Атака"
			"Action_FighterSwingMedium"		"Обычный удар"
			"Action_FireWeapon"		"Стрелять из оружия"
			"Action_HidePauseMenu"		"Скрыть меню паузы"
			"Action_Inspect"		"Осмотреть"
			"Action_Jump"		"Прыжок"
			"Action_LookXY"		"Смотреть"
			"Action_MeleeChannel"		"Концентрация ближ. боя"
			"Action_MenuBack"		"Назад"
			"Action_MenuDown"		"Вниз"
			"Action_MenuGeneric1"		"Действие 1"
			"Action_MenuGeneric2"		"Действие 2"
			"Action_MenuGeneric3"		"Действие 3"
			"Action_MenuGeneric4"		"Действие 4"
			"Action_MenuGeneric5"		"Действие 5"
			"Action_MenuLThumb"		"Нажатие левого стика"
			"Action_MenuLeft"		"Влево"
			"Action_MenuLookXY"		"Вторичное движение"
			"Action_MenuMoveXY"		"Движение"
			"Action_MenuNextMenu"		"Следующее меню"
			"Action_MenuPreviousMenu"		"Предыдущее меню"
			"Action_MenuRThumb"		"Нажатие правого стика"
			"Action_MenuRight"		"Вправо"
			"Action_MenuSelect"		"Выбор"
			"Action_MenuToggleChat"		"Чат"
			"Action_MenuUp"		"Вверх"
			"Action_MoveXZ"		"Движение"
			"Action_NextPower"		"Следующая способность"
			"Action_PlaceMarker"		"Поставить маркер"
			"Action_PowerMenuAbility0"		"Способность A (меню способностей)"
			"Action_PowerMenuAbility1"		"Способность B (меню способностей)"
			"Action_PowerMenuAbility2"		"Способность C (меню способностей)"
			"Action_PowerMenuAbility3"		"Способность D (меню способностей)"
			"Action_PowerMenuAbility4"		"Способность E (меню способностей)"
			"Action_PreviousPower"		"Предыдущая способность"
			"Action_QuickMelee"		"Быстрый удар"
			"Action_Reload"		"Перезарядка"
			"Action_ReverseCamera"		"Сменить камеру"
			"Action_ScoopChannel"		"Метнуть"
			"Action_ScoopParry"		"Притяжение мяча"
			"Action_ScoopPass"		"Пас"
			"Action_ScoopSwing"		"Удар"
			"Action_SecondaryFire"		"Вторичный огонь"
			"Action_ShawzinChangeInstrument"		"Сменить инструмент"
			"Action_ShawzinChangeScale"		"Изменить масштаб"
			"Action_ShawzinExit"		"Выход"
			"Action_ShawzinFretAltOne"		"Лад Неба (альтернативный)"
			"Action_ShawzinFretAltThree"		"Лад Воды (альтернативный)"
			"Action_ShawzinFretAltTwo"		"Лад Земли (альтернативный)"
			"Action_ShawzinFretFour"		"Четвёртый Лад"
			"Action_ShawzinFretOne"		"Лад Неба"
			"Action_ShawzinFretThree"		"Лад Воды"
			"Action_ShawzinFretTwo"		"Лад Земли"
			"Action_ShawzinNoteOne"		"Первая Нота"
			"Action_ShawzinNoteThree"		"Третья Нота"
			"Action_ShawzinNoteTwo"		"Вторая Нота"
			"Action_ShawzinNoteWhammy"		"Эффект Вамми"
			"Action_ShawzinSongList"		"Список песен"
			"Action_ShawzinStepSequencer"		"Пошаговая последовательность"
			"Action_ShowConsumablesMenu"		"Всплывающие элементы"
			"Action_ShowLevelMap"		"Отобразить карту уровня"
			"Action_ShowPauseMenu"		"Показать меню паузы"
			"Action_ShowPowerMenu"		"Меню способностей"
			"Action_SlowMovement"		"Медленное движение"
			"Action_Sprint"		"Бежать"
			"Action_SwitchWeapon"		"Сменить оружие"
			"Action_TiltYaw"		"Наклон вправо"
			"Action_TiltYawInvert"		"Наклон влево"
			"Action_ToggleFreeCamera"		"Вкл. свободную камеру"
			"Action_UseGearItem0"		"Клавиша снаряжения 1"
			"Action_UseGearItem1"		"Клавиша снаряжения 2"
			"Action_UseGearItem2"		"Клавиша снаряжения 3"
			"Action_UseGearItem3"		"Клавиша снаряжения 4"
			"Action_UseGearItem4"		"Клавиша снаряжения 5"
			"Action_UseGearItem5"		"Клавиша снаряжения 6"
			"Action_UseGearItem6"		"Клавиша снаряжения 7"
			"Action_UseGearItem7"		"Клавиша снаряжения 8"
			"Action_UsePower"		"Использовать выбранную способность"
			"Action_ViewHumanPlayers"		"Отображать список игроков"
			"Action_ViewRailjackSystems"		"Просмотр систем Рэйлджека"
			"Set_Captura"		"Каптура"
			"Set_Decoration"		"Украшение"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Лаунчер"
			"Set_Lunaro"		"Лунаро"
			"Set_Menu"		"Меню"
			"Set_Shawzin"		"Шазин"
			"Set_Warframe"		"Игра"
			"Set_WarframeMelee"		"Режим ближнего боя"
		}
		"polish"
		{
			"Action_AccelY"		"W górę"
			"Action_AccelYInvert"		"W dół"
			"Action_ActivateAbility0"		"Umiejętność A"
			"Action_ActivateAbility1"		"Umiejętność B"
			"Action_ActivateAbility2"		"Umiejętność C"
			"Action_ActivateAbility3"		"Umiejętność D"
			"Action_ActivateAbility4"		"Umiejętność E"
			"Action_AimWeapon"		"Celowanie"
			"Action_ArchwingAscend"		"W górę (Archwing/Dekoracje)"
			"Action_ArchwingDescend"		"W dół (Archwing/Dekoracje)"
			"Action_CapturaAdvanceTime"		"Wznów czas"
			"Action_CapturaExit"		"Wyłącz swobodną kamerę"
			"Action_CapturaPanelVisibility"		"Widoczność panelu"
			"Action_CapturaToggleControl"		"Ustawienia"
			"Action_ContextAction"		"Akcja kontekstowa"
			"Action_Crouch"		"Kucnięcie, ślizg i przewrót"
			"Action_DecorationAdd"		"Dodaj Dekorację"
			"Action_DecorationEdit"		"Dostosuj Dekorację"
			"Action_DecorationExit"		"Wstecz"
			"Action_DecorationMode"		"Tryb zaawansowany"
			"Action_DecorationPlace"		"Umieść Dekorację"
			"Action_DecorationPushPull"		"Przyciągnij/Oddal Dekorację"
			"Action_DecorationReset"		"Resetuj"
			"Action_DecorationRotate"		"Przytrzymaj, aby obrócić"
			"Action_DecorationRotation"		"Zmień oś obrotu"
			"Action_DecorationScale"		"Przytrzymaj, aby skalować"
			"Action_DecorationScaleDown"		"Pomniejsz"
			"Action_DecorationScaleUp"		"Powiększ"
			"Action_DecorationSnap"		"Przyciąganie do siatki"
			"Action_DecorationSurfaceSnap"		"Przyciąganie do powierzchni"
			"Action_EquipRailjackRepairTool"		"Wyposaż Omni"
			"Action_FastMovement"		"Szybki ruch"
			"Action_FighterGrab"		"Chwyt"
			"Action_FighterMoveDown"		"Kucanie"
			"Action_FighterMoveLeft"		"Ruch w lewo"
			"Action_FighterMoveRight"		"Ruch w prawo"
			"Action_FighterMoveUp"		"Skok"
			"Action_FighterParry"		"Blok"
			"Action_FighterPower"		"Atak specjalny"
			"Action_FighterSwing"		"Lekki atak"
			"Action_FighterSwingHeavy"		"Ciężki atak"
			"Action_FighterSwingMedium"		"Średni atak"
			"Action_FireWeapon"		"Strzał z broni"
			"Action_HidePauseMenu"		"Ukryj menu pauzy"
			"Action_Inspect"		"Zbadaj"
			"Action_Jump"		"Skok"
			"Action_LookXY"		"Rozglądanie"
			"Action_MeleeChannel"		"Przewodzenie broni białej"
			"Action_MenuBack"		"Wstecz"
			"Action_MenuDown"		"W dół"
			"Action_MenuGeneric1"		"Czynność 1"
			"Action_MenuGeneric2"		"Czynność 2"
			"Action_MenuGeneric3"		"Czynność 3"
			"Action_MenuGeneric4"		"Czynność 4"
			"Action_MenuGeneric5"		"Czynność 5"
			"Action_MenuLThumb"		"Przyciśnięcie Lewej Gałki"
			"Action_MenuLeft"		"W lewo"
			"Action_MenuLookXY"		"Ruch alternatywny"
			"Action_MenuMoveXY"		"Ruch"
			"Action_MenuNextMenu"		"Następne menu"
			"Action_MenuPreviousMenu"		"Poprzednie menu"
			"Action_MenuRThumb"		"Przyciśnięcie Prawej Gałki"
			"Action_MenuRight"		"W prawo"
			"Action_MenuSelect"		"Wybierz"
			"Action_MenuToggleChat"		"Czat"
			"Action_MenuUp"		"W górę"
			"Action_MoveXZ"		"Ruch"
			"Action_NextPower"		"Następna Umiejętność"
			"Action_PlaceMarker"		"Umieść znacznik"
			"Action_PlayEmote0"		" "
			"Action_PlayEmote1"		" "
			"Action_PlayEmote10"		" "
			"Action_PlayEmote11"		" "
			"Action_PlayEmote2"		" "
			"Action_PlayEmote3"		" "
			"Action_PlayEmote4"		" "
			"Action_PlayEmote5"		" "
			"Action_PlayEmote6"		" "
			"Action_PlayEmote7"		" "
			"Action_PlayEmote8"		" "
			"Action_PlayEmote9"		" "
			"Action_PowerMenuAbility0"		"Umiejętność A (Menu Umiejętności)"
			"Action_PowerMenuAbility1"		"Umiejętność B (Menu Umiejętności)"
			"Action_PowerMenuAbility2"		"Umiejętność C (Menu Umiejętności)"
			"Action_PowerMenuAbility3"		"Umiejętność D (Menu Umiejętności)"
			"Action_PowerMenuAbility4"		"Umiejętność E (Menu Umiejętności)"
			"Action_PreviousPower"		"Poprzednia Umiejętność"
			"Action_QuickMelee"		"Szybki atak wręcz"
			"Action_Reload"		"Przeładowanie"
			"Action_ReverseCamera"		"Zmień kamerę"
			"Action_ScoopChannel"		"Rzut"
			"Action_ScoopParry"		"Przyciąganie Kuli"
			"Action_ScoopPass"		"Podanie"
			"Action_ScoopSwing"		"Orientacja"
			"Action_SecondaryFire"		"Ogień alternatywny"
			"Action_ShawzinChangeInstrument"		"Zmień Instrument"
			"Action_ShawzinChangeScale"		"Zmień skalę"
			"Action_ShawzinExit"		"Wyjście"
			"Action_ShawzinFretAltOne"		"Alternatywny Próg Nieba"
			"Action_ShawzinFretAltThree"		"Alternatywny Próg Wody"
			"Action_ShawzinFretAltTwo"		"Alternatywny Próg Ziemi"
			"Action_ShawzinFretFour"		"Próg Czwarty"
			"Action_ShawzinFretOne"		"Próg Nieba"
			"Action_ShawzinFretThree"		"Próg Wody"
			"Action_ShawzinFretTwo"		"Próg Ziemi"
			"Action_ShawzinNoteOne"		"Nuta Pierwsza"
			"Action_ShawzinNoteThree"		"Nuta Trzecia"
			"Action_ShawzinNoteTwo"		"Nuta Druga"
			"Action_ShawzinNoteWhammy"		"Wajcha Nuty"
			"Action_ShawzinSongList"		"Lista Utworów"
			"Action_ShawzinStepSequencer"		"Sekwencer"
			"Action_ShowConsumablesMenu"		"Powiadomienie przedmiotu"
			"Action_ShowLevelMap"		"Pokaż mapę poziomu"
			"Action_ShowPauseMenu"		"Pokaż menu pauzy"
			"Action_ShowPowerMenu"		"Menu Umiejętności"
			"Action_SlowMovement"		"Precyzyjny Ruch"
			"Action_Sprint"		"Sprint"
			"Action_SwitchWeapon"		"Zmiana broni"
			"Action_TiltYaw"		"Przechyl w prawo"
			"Action_TiltYawInvert"		"Przechyl w lewo"
			"Action_ToggleFreeCamera"		"Przełącz swobodną kamerę"
			"Action_UseGearItem0"		"Przycisk Sprzętu 1"
			"Action_UseGearItem1"		"Przycisk Sprzętu 2"
			"Action_UseGearItem10"		" "
			"Action_UseGearItem11"		" "
			"Action_UseGearItem2"		"Przycisk Sprzętu 3"
			"Action_UseGearItem3"		"Przycisk Sprzętu 4"
			"Action_UseGearItem4"		"Przycisk Sprzętu 5"
			"Action_UseGearItem5"		"Przycisk Sprzętu 6"
			"Action_UseGearItem6"		"Przycisk Sprzętu 7"
			"Action_UseGearItem7"		"Przycisk Sprzętu 8"
			"Action_UseGearItem8"		" "
			"Action_UseGearItem9"		" "
			"Action_UsePower"		"Użyj wybranej Umiejętności"
			"Action_ViewHumanPlayers"		"Pokaż listę graczy"
			"Action_ViewRailjackSystems"		"Wyświetl systemy Railjacka"
			"Set_Captura"		"Fotobudka"
			"Set_Decoration"		"Dekorowanie"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Program startowy"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menu"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Rozgrywka"
			"Set_WarframeMelee"		"Walka wręcz"
		}
		"ukrainian"
		{
			"Action_AccelY"		"Вище"
			"Action_AccelYInvert"		"Нижче"
			"Action_ActivateAbility0"		"Здібність 1"
			"Action_ActivateAbility1"		"Здібність 2"
			"Action_ActivateAbility2"		"Здібність 3"
			"Action_ActivateAbility3"		"Здібність 4"
			"Action_ActivateAbility4"		"Здібність 5"
			"Action_AimWeapon"		"Прицілитися"
			"Action_ArchwingAscend"		"Вгору (арквінґ/оздоблення)"
			"Action_ArchwingDescend"		"Вниз (арквінґ/оздоблення)"
			"Action_CapturaAdvanceTime"		"Пришвидшити час"
			"Action_CapturaExit"		"Вимкнути режим вільного огляду"
			"Action_CapturaPanelVisibility"		"Змінити видимість панелі"
			"Action_CapturaToggleControl"		"Налаштування"
			"Action_ContextAction"		"Взаємодіяти"
			"Action_Crouch"		"Сісти, ковзати, перекочуватися"
			"Action_DecorationAdd"		"Додати нове оздоблення"
			"Action_DecorationEdit"		"Налаштувати оздоблення"
			"Action_DecorationExit"		"Назад"
			"Action_DecorationMode"		"Розширений режим"
			"Action_DecorationPlace"		"Розмістити оздоблення"
			"Action_DecorationPushPull"		"Відштовхнути/притягнути оздоблення"
			"Action_DecorationReset"		"Скинути"
			"Action_DecorationRotate"		"Утримувати для обертання"
			"Action_DecorationRotation"		"Змінити вісь обертання"
			"Action_DecorationScale"		"Утримувати для масштабування"
			"Action_DecorationScaleDown"		"Зменшити"
			"Action_DecorationScaleUp"		"Збільшити"
			"Action_DecorationSnap"		"Прив’язати"
			"Action_DecorationSurfaceSnap"		"Прив’язування до поверхні"
			"Action_EquipRailjackRepairTool"		"Спорядити мультиінструмент"
			"Action_FastMovement"		"Пришвидшити рух"
			"Action_FighterGrab"		"Схопити"
			"Action_FighterMoveDown"		"Присісти"
			"Action_FighterMoveLeft"		"Рухатися ліворуч"
			"Action_FighterMoveRight"		"Рухатися праворуч"
			"Action_FighterMoveUp"		"Стрибнути"
			"Action_FighterParry"		"Блокувати"
			"Action_FighterPower"		"Особлива атака"
			"Action_FighterSwing"		"Слабкий удар"
			"Action_FighterSwingHeavy"		"Потужний удар"
			"Action_FighterSwingMedium"		"Звичайний удар"
			"Action_FireWeapon"		"Стріляти"
			"Action_HidePauseMenu"		"Приховати меню паузи"
			"Action_Inspect"		"Оглянути"
			"Action_Jump"		"Стрибнути"
			"Action_LookXY"		"Поглянути"
			"Action_MeleeChannel"		"Концентрація з холодною зброєю"
			"Action_MenuBack"		"Назад"
			"Action_MenuDown"		"Униз"
			"Action_MenuGeneric1"		"Варіант 1"
			"Action_MenuGeneric2"		"Варіант 2"
			"Action_MenuGeneric3"		"Варіант 3"
			"Action_MenuGeneric4"		"Варіант 4"
			"Action_MenuGeneric5"		"Варіант 5"
			"Action_MenuLThumb"		"Клацання лівого стика"
			"Action_MenuLeft"		"Ліворуч"
			"Action_MenuLookXY"		"Альтернативний режим руху"
			"Action_MenuMoveXY"		"Уперед"
			"Action_MenuNextMenu"		"Наступне меню"
			"Action_MenuPreviousMenu"		"Попереднє меню"
			"Action_MenuRThumb"		"Клацання правого стика"
			"Action_MenuRight"		"Праворуч"
			"Action_MenuSelect"		"Вибрати"
			"Action_MenuToggleChat"		"Показати чат"
			"Action_MenuUp"		"Угору"
			"Action_MoveXZ"		"Уперед"
			"Action_NextPower"		"Наступна здібність"
			"Action_PlaceMarker"		"Розмістити позначку"
			"Action_PowerMenuAbility0"		"Здібність 1 (меню здібностей)"
			"Action_PowerMenuAbility1"		"Здібність 2 (меню здібностей)"
			"Action_PowerMenuAbility2"		"Здібність 3 (меню здібностей)"
			"Action_PowerMenuAbility3"		"Здібність 4 (меню здібностей)"
			"Action_PowerMenuAbility4"		"Здібність 5 (меню здібностей)"
			"Action_PreviousPower"		"Попередня здібність"
			"Action_QuickMelee"		"Швидкий удар холодною зброєю"
			"Action_Reload"		"Перезарядити"
			"Action_ReverseCamera"		"Перевернути камеру"
			"Action_ScoopChannel"		"Кинути"
			"Action_ScoopParry"		"Притягнути лунаро"
			"Action_ScoopPass"		"Передати"
			"Action_ScoopSwing"		"Відібрати"
			"Action_SecondaryFire"		"Альтернативний режим стрільби"
			"Action_ShawzinChangeInstrument"		"Змінити інструмент"
			"Action_ShawzinChangeScale"		"Змінити масштаб"
			"Action_ShawzinExit"		"Вийти"
			"Action_ShawzinFretAltOne"		"Лад Неба (додатковий)"
			"Action_ShawzinFretAltThree"		"Лад Води (додатковий)"
			"Action_ShawzinFretAltTwo"		"Лад Землі (додатковий)"
			"Action_ShawzinFretFour"		"Лад 4"
			"Action_ShawzinFretOne"		"Лад Неба"
			"Action_ShawzinFretThree"		"Лад Води"
			"Action_ShawzinFretTwo"		"Лад Землі"
			"Action_ShawzinNoteOne"		"Нота 1"
			"Action_ShawzinNoteThree"		"Нота 2"
			"Action_ShawzinNoteTwo"		"Нота 3"
			"Action_ShawzinNoteWhammy"		"Нотне вібрато"
			"Action_ShawzinSongList"		"Перелік пісень"
			"Action_ShawzinStepSequencer"		"Послідовність кроків"
			"Action_ShowConsumablesMenu"		"Меню витратників"
			"Action_ShowLevelMap"		"Показати мапу рівня"
			"Action_ShowPauseMenu"		"Показати меню паузи"
			"Action_ShowPowerMenu"		"Меню здібностей"
			"Action_SlowMovement"		"Сповільнити рух"
			"Action_Sprint"		"Бігти"
			"Action_SwitchWeapon"		"Змінити зброю"
			"Action_TiltYaw"		"Нахил праворуч"
			"Action_TiltYawInvert"		"Нахил ліворуч"
			"Action_ToggleFreeCamera"		"Перемкнути режим вільного огляду"
			"Action_UseGearItem0"		"Гар. клавіша спорядження 1"
			"Action_UseGearItem1"		"Гар. клавіша спорядження 2"
			"Action_UseGearItem2"		"Гар. клавіша спорядження 3"
			"Action_UseGearItem3"		"Гар. клавіша спорядження 4"
			"Action_UseGearItem4"		"Гар. клавіша спорядження 5"
			"Action_UseGearItem5"		"Гар. клавіша спорядження 6"
			"Action_UseGearItem6"		"Гар. клавіша спорядження 7"
			"Action_UseGearItem7"		"Гар. клавіша спорядження 8"
			"Action_UsePower"		"Використати обрану здібність"
			"Action_ViewHumanPlayers"		"Показувати перелік гравців"
			"Action_ViewRailjackSystems"		"Переглянути рушії рейкоджека"
			"Set_Captura"		"Світлописець"
			"Set_Decoration"		"Оздоблення"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Запускач"
			"Set_Lunaro"		"Лунаро"
			"Set_Menu"		"Меню"
			"Set_Shawzin"		"Шодзін"
			"Set_Warframe"		"Гра"
			"Set_WarframeMelee"		"Ближній бій"
		}
		"turkish"
		{
			"Action_AccelY"		"Yüksel"
			"Action_AccelYInvert"		"Alçal"
			"Action_ActivateAbility0"		"Yetenek A"
			"Action_ActivateAbility1"		"Yetenek B"
			"Action_ActivateAbility2"		"Yetenek C"
			"Action_ActivateAbility3"		"Yetenek D"
			"Action_ActivateAbility4"		"Yetenek E"
			"Action_AimWeapon"		"Nişan al"
			"Action_ArchwingAscend"		"Yukarı çık (Archwing/Dekorasyon)"
			"Action_ArchwingDescend"		"Aşağı in (Archwing/Dekorasyon)"
			"Action_CapturaAdvanceTime"		"Zamanı ilerlet"
			"Action_CapturaExit"		"Serbest kameradan çık"
			"Action_CapturaPanelVisibility"		"Panel Görünürlüğünü Aç/Kapa"
			"Action_CapturaToggleControl"		"Ayarlar"
			"Action_ContextAction"		"Etkileşim eylemi"
			"Action_Crouch"		"Çömel, kay ve yuvarlan"
			"Action_DecorationAdd"		"Yeni Dekorasyon ekle"
			"Action_DecorationEdit"		"Dekorasyonu değiştir"
			"Action_DecorationExit"		"Geri"
			"Action_DecorationMode"		"Gelişmiş Mod"
			"Action_DecorationPlace"		"Dekorasyonu Ekle"
			"Action_DecorationPushPull"		"Dekorasyonu İt/Çek"
			"Action_DecorationReset"		"Sıfırla"
			"Action_DecorationRotate"		"Döndürmek için basılı tut"
			"Action_DecorationRotation"		"Döndürme Eksenini değiştir"
			"Action_DecorationScale"		"Ölçeklemek için basılı tut"
			"Action_DecorationScaleDown"		"Küçült"
			"Action_DecorationScaleUp"		"Büyüt"
			"Action_DecorationSnap"		"Konumlandır"
			"Action_DecorationSurfaceSnap"		"Yüzeye Konumlandır"
			"Action_EquipRailjackRepairTool"		"Omni'yi Kuşan"
			"Action_FastMovement"		"Hızlı İlerleme"
			"Action_FighterGrab"		"Yakala"
			"Action_FighterMoveDown"		"Eğil"
			"Action_FighterMoveLeft"		"Sola İlerle"
			"Action_FighterMoveRight"		"Sağa İlerle"
			"Action_FighterMoveUp"		"Zıpla"
			"Action_FighterParry"		"Blokla"
			"Action_FighterPower"		"Özel"
			"Action_FighterSwing"		"Hafif Saldırı"
			"Action_FighterSwingHeavy"		"Ağır Saldırı"
			"Action_FighterSwingMedium"		"Orta Saldırı"
			"Action_FireWeapon"		"Ateş etme"
			"Action_HidePauseMenu"		"Duraklatma menüsünü gizle"
			"Action_Inspect"		"İncele"
			"Action_Jump"		"Zıpla"
			"Action_LookXY"		"Bakma"
			"Action_MeleeChannel"		"Yakın Dövüş Kanalize"
			"Action_MenuBack"		"Geri"
			"Action_MenuDown"		"Aşağı"
			"Action_MenuGeneric1"		"Eylem 1"
			"Action_MenuGeneric2"		"Eylem 2"
			"Action_MenuGeneric3"		"Eylem 3"
			"Action_MenuGeneric4"		"Eylem 4"
			"Action_MenuGeneric5"		"Eylem 5"
			"Action_MenuLThumb"		"Sol Çubuk Tıklama"
			"Action_MenuLeft"		"Sol"
			"Action_MenuLookXY"		"İkincil hareket"
			"Action_MenuMoveXY"		"Hareket"
			"Action_MenuNextMenu"		"Sonraki menü"
			"Action_MenuPreviousMenu"		"Önceki menü"
			"Action_MenuRThumb"		"Sağ Çubuk Tıklama"
			"Action_MenuRight"		"Sağ"
			"Action_MenuSelect"		"Seç"
			"Action_MenuToggleChat"		"Sohbet"
			"Action_MenuUp"		"Yukarı"
			"Action_MoveXZ"		"Hareket"
			"Action_NextPower"		"Sonraki yetenek"
			"Action_PlaceMarker"		"Yer işareti"
			"Action_PowerMenuAbility0"		"Yetenek A (Yetenek menüsü)"
			"Action_PowerMenuAbility1"		"Yetenek B (Yetenek menüsü)"
			"Action_PowerMenuAbility2"		"Yetenek C (Yetenek menüsü)"
			"Action_PowerMenuAbility3"		"Yetenek D (Yetenek menüsü)"
			"Action_PowerMenuAbility4"		"Yetenek E (Yetenek menüsü)"
			"Action_PreviousPower"		"Önceki yetenek"
			"Action_QuickMelee"		"Hızlı yakın dövüş"
			"Action_Reload"		"Silahı Doldur"
			"Action_ReverseCamera"		"Kamerayı geri al"
			"Action_ScoopChannel"		"Fırlat"
			"Action_ScoopParry"		"Top Mıknatısı"
			"Action_ScoopPass"		"Geç"
			"Action_ScoopSwing"		"Kontrol"
			"Action_SecondaryFire"		"İkincil ateş"
			"Action_ShawzinChangeInstrument"		"Enstrüman Değiştir"
			"Action_ShawzinChangeScale"		"Ölçeği Değiştir"
			"Action_ShawzinExit"		"Çıkış"
			"Action_ShawzinFretAltOne"		"Gökyüzü Perdesi Alt"
			"Action_ShawzinFretAltThree"		"Su Perdesi Alt"
			"Action_ShawzinFretAltTwo"		"Dünya Perdesi Alt"
			"Action_ShawzinFretFour"		"Perde Dört"
			"Action_ShawzinFretOne"		"Gökyüzü Perdesi"
			"Action_ShawzinFretThree"		"Su Perdesi"
			"Action_ShawzinFretTwo"		"Dünya Perdesi"
			"Action_ShawzinNoteOne"		"Birinci Nota"
			"Action_ShawzinNoteThree"		"Üçüncü Nota"
			"Action_ShawzinNoteTwo"		"İkinci Nota"
			"Action_ShawzinNoteWhammy"		"Nota Akordu"
			"Action_ShawzinSongList"		"Şarkı Listesi"
			"Action_ShawzinStepSequencer"		"Adım Sıralayıcı"
			"Action_ShowConsumablesMenu"		"Eşya açılır penceresi"
			"Action_ShowLevelMap"		"Görev haritasını göster"
			"Action_ShowPauseMenu"		"Duraklatma menüsünü göster"
			"Action_ShowPowerMenu"		"Yetenek menüsü"
			"Action_SlowMovement"		"Yavaş İlerleme"
			"Action_Sprint"		"Depar"
			"Action_SwitchWeapon"		"Silah değiştir"
			"Action_TiltYaw"		"Sağa eğim"
			"Action_TiltYawInvert"		"Sola eğim"
			"Action_ToggleFreeCamera"		"Serbest kamera aç/kapa"
			"Action_UseGearItem0"		"Teçhizat tuşu 1"
			"Action_UseGearItem1"		"Teçhizat tuşu 2"
			"Action_UseGearItem2"		"Teçhizat tuşu 3"
			"Action_UseGearItem3"		"Teçhizat tuşu 4"
			"Action_UseGearItem4"		"Teçhizat tuşu 5"
			"Action_UseGearItem5"		"Teçhizat tuşu 6"
			"Action_UseGearItem6"		"Teçhizat tuşu 7"
			"Action_UseGearItem7"		"Teçhizat tuşu 8"
			"Action_UsePower"		"Seçili yeteneği kullan"
			"Action_ViewHumanPlayers"		"Oyuncu listesini göster"
			"Action_ViewRailjackSystems"		"Railjack Sistemlerini Görüntüle"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"Dekorasyon"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Başlatıcı"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"Menü"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"Oyun"
			"Set_WarframeMelee"		"Yakın Dövüş Modu"
		}
		"japanese"
		{
			"Action_AccelY"		"登る"
			"Action_AccelYInvert"		"降りる"
			"Action_ActivateAbility0"		"アビリティ A"
			"Action_ActivateAbility1"		"アビリティ B"
			"Action_ActivateAbility2"		"アビリティ C"
			"Action_ActivateAbility3"		"アビリティ D"
			"Action_ActivateAbility4"		"アビリティ E"
			"Action_AimWeapon"		"武器エイム"
			"Action_ArchwingAscend"		"上移動（アークウイング・デコレーション）"
			"Action_ArchwingDescend"		"下移動（アークウイング・デコレーション）"
			"Action_CapturaAdvanceTime"		"時間を進める"
			"Action_CapturaExit"		"フリーカメラ終了"
			"Action_CapturaPanelVisibility"		"パネル表示の切り替え"
			"Action_CapturaToggleControl"		"設定"
			"Action_ContextAction"		"コンテキストアクション"
			"Action_Crouch"		"しゃがみ、スライディング、ローリング"
			"Action_DecorationAdd"		"新しいデコレーションを追加する"
			"Action_DecorationEdit"		"デコレーションを編集"
			"Action_DecorationExit"		"戻る"
			"Action_DecorationMode"		"アドバンスモード"
			"Action_DecorationPlace"		"デコレーションの設置"
			"Action_DecorationPushPull"		"デコレーションの押し出し・引き寄せ"
			"Action_DecorationReset"		"リセット"
			"Action_DecorationRotate"		"長押しで回転"
			"Action_DecorationRotation"		"回転軸の変更"
			"Action_DecorationScale"		"長押しで拡縮"
			"Action_DecorationScaleDown"		"縮小"
			"Action_DecorationScaleUp"		"拡大"
			"Action_DecorationSnap"		"スナップ"
			"Action_DecorationSurfaceSnap"		"面吸着"
			"Action_EquipRailjackRepairTool"		"オムニを装備する"
			"Action_FastMovement"		"高速移動"
			"Action_FighterGrab"		"つかむ"
			"Action_FighterMoveDown"		"しゃがむ"
			"Action_FighterMoveLeft"		"左移動"
			"Action_FighterMoveRight"		"右移動"
			"Action_FighterMoveUp"		"ジャンプ"
			"Action_FighterParry"		"ブロック"
			"Action_FighterPower"		"特殊"
			"Action_FighterSwing"		"弱攻撃"
			"Action_FighterSwingHeavy"		"強攻撃"
			"Action_FighterSwingMedium"		"中攻撃"
			"Action_FireWeapon"		"武器を発射"
			"Action_HidePauseMenu"		"ポーズ画面を隠す"
			"Action_Inspect"		"調べる"
			"Action_Jump"		"ジャンプ"
			"Action_LookXY"		"カメラ操作"
			"Action_MeleeChannel"		"近接チャネリング"
			"Action_MenuBack"		"戻る"
			"Action_MenuDown"		"下"
			"Action_MenuGeneric1"		"アクション 1"
			"Action_MenuGeneric2"		"アクション 2"
			"Action_MenuGeneric3"		"アクション 3"
			"Action_MenuGeneric4"		"アクション 4"
			"Action_MenuGeneric5"		"アクション 5"
			"Action_MenuLThumb"		"左スティッククリック"
			"Action_MenuLeft"		"左"
			"Action_MenuLookXY"		"セカンダリ移動"
			"Action_MenuMoveXY"		"移動"
			"Action_MenuNextMenu"		"次メニュー"
			"Action_MenuPreviousMenu"		"前メニュー"
			"Action_MenuRThumb"		"右スティッククリック"
			"Action_MenuRight"		"右"
			"Action_MenuSelect"		"選択"
			"Action_MenuToggleChat"		"チャット"
			"Action_MenuUp"		"上"
			"Action_MoveXZ"		"移動"
			"Action_NextPower"		"次アビリティ"
			"Action_PlaceMarker"		"マーカーを設置"
			"Action_PowerMenuAbility0"		"アビリティ A （アビリティメニュー）"
			"Action_PowerMenuAbility1"		"アビリティ B （アビリティメニュー）"
			"Action_PowerMenuAbility2"		"アビリティ C （アビリティメニュー）"
			"Action_PowerMenuAbility3"		"アビリティ D （アビリティメニュー）"
			"Action_PowerMenuAbility4"		"アビリティ E （アビリティメニュー）"
			"Action_PreviousPower"		"前アビリティ"
			"Action_QuickMelee"		"クイック近接"
			"Action_Reload"		"リロード"
			"Action_ReverseCamera"		"カメラ反転"
			"Action_ScoopChannel"		"投球"
			"Action_ScoopParry"		"ボール吸収"
			"Action_ScoopPass"		"パス"
			"Action_ScoopSwing"		"チェック"
			"Action_SecondaryFire"		"セカンダリ射撃"
			"Action_ShawzinChangeInstrument"		"楽器を変える"
			"Action_ShawzinChangeScale"		"スケールを変更"
			"Action_ShawzinExit"		"終わる"
			"Action_ShawzinFretAltOne"		"スカイ・フレットオルタ"
			"Action_ShawzinFretAltThree"		"ウォーター・フレットオルタ"
			"Action_ShawzinFretAltTwo"		"アース・フレットオルタ"
			"Action_ShawzinFretFour"		"フレット・フォー"
			"Action_ShawzinFretOne"		"スカイ・フレット"
			"Action_ShawzinFretThree"		"ウォーター・フレット"
			"Action_ShawzinFretTwo"		"アース・フレット"
			"Action_ShawzinNoteOne"		"ノート・ワン"
			"Action_ShawzinNoteThree"		"ノート・スリー"
			"Action_ShawzinNoteTwo"		"ノート・ツー"
			"Action_ShawzinNoteWhammy"		"ノート・ワミー"
			"Action_ShawzinSongList"		"ソングリスト"
			"Action_ShawzinStepSequencer"		"ステップ・シーケンサー"
			"Action_ShowConsumablesMenu"		"アイテムポップアップ"
			"Action_ShowLevelMap"		"マップを表示"
			"Action_ShowPauseMenu"		"ポーズ画面を表示"
			"Action_ShowPowerMenu"		"アビリティメニュー"
			"Action_SlowMovement"		"速度低下"
			"Action_Sprint"		"ダッシュ"
			"Action_SwitchWeapon"		"武器切り替え"
			"Action_TiltYaw"		"右に傾ける"
			"Action_TiltYawInvert"		"左に傾ける"
			"Action_ToggleFreeCamera"		"フリーカメラに切り替え"
			"Action_UseGearItem0"		"ギア ホットキー 1"
			"Action_UseGearItem1"		"ギア ホットキー 2"
			"Action_UseGearItem2"		"ギア ホットキー 3"
			"Action_UseGearItem3"		"ギア ホットキー 4"
			"Action_UseGearItem4"		"ギア ホットキー 5"
			"Action_UseGearItem5"		"ギア ホットキー 6"
			"Action_UseGearItem6"		"ギア ホットキー 7"
			"Action_UseGearItem7"		"ギア ホットキー 8"
			"Action_UsePower"		"選択したアビリティを発動"
			"Action_ViewHumanPlayers"		"プレイヤーリスト表示"
			"Action_ViewRailjackSystems"		"レールジャックシステム表示"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"デコレーション"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"ランチャー"
			"Set_Lunaro"		"ルナーロ"
			"Set_Menu"		"メニュー"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"ゲーム"
			"Set_WarframeMelee"		"近接モード"
		}
		"schinese"
		{
			"Action_AccelY"		"升高"
			"Action_AccelYInvert"		"降低"
			"Action_ActivateAbility0"		"技能 A"
			"Action_ActivateAbility1"		"技能 B"
			"Action_ActivateAbility2"		"技能 C"
			"Action_ActivateAbility3"		"技能 D"
			"Action_ActivateAbility4"		"技能 E"
			"Action_AimWeapon"		"瞄准武器"
			"Action_ArchwingAscend"		"向上移动（Archwing/装饰）"
			"Action_ArchwingDescend"		"向上移动（Archwing/装饰）"
			"Action_CapturaAdvanceTime"		"时间推进"
			"Action_CapturaExit"		"退出自由视角"
			"Action_CapturaPanelVisibility"		"切换面板可视度"
			"Action_CapturaToggleControl"		"设置"
			"Action_ContextAction"		"场景互动"
			"Action_Crouch"		"蹲下，滑行，并翻滚"
			"Action_DecorationAdd"		"添加新装饰"
			"Action_DecorationEdit"		"编辑装饰"
			"Action_DecorationExit"		"返回"
			"Action_DecorationMode"		"高级模式"
			"Action_DecorationPlace"		"放置装饰"
			"Action_DecorationPushPull"		"推远/拉近装饰"
			"Action_DecorationReset"		"重置"
			"Action_DecorationRotate"		"按住以旋转"
			"Action_DecorationRotation"		"改变旋转轴"
			"Action_DecorationScale"		"按住以缩放"
			"Action_DecorationScaleDown"		"缩小"
			"Action_DecorationScaleUp"		"放大"
			"Action_DecorationSnap"		"自动吸附"
			"Action_DecorationSurfaceSnap"		"表面吸附"
			"Action_EquipRailjackRepairTool"		"装备万用工具"
			"Action_FastMovement"		"快动作"
			"Action_FighterGrab"		"抓取"
			"Action_FighterMoveDown"		"下蹲"
			"Action_FighterMoveLeft"		"向左移动"
			"Action_FighterMoveRight"		"向右移动"
			"Action_FighterMoveUp"		"跳跃"
			"Action_FighterParry"		"格挡"
			"Action_FighterPower"		"特殊"
			"Action_FighterSwing"		"轻击"
			"Action_FighterSwingHeavy"		"重击"
			"Action_FighterSwingMedium"		"中击"
			"Action_FireWeapon"		"开火"
			"Action_HidePauseMenu"		"隐藏暂停菜单"
			"Action_Inspect"		"检视"
			"Action_Jump"		"跳跃"
			"Action_LookXY"		"造型"
			"Action_MeleeChannel"		"近战导引"
			"Action_MenuBack"		"返回"
			"Action_MenuDown"		"下"
			"Action_MenuGeneric1"		"动作 1"
			"Action_MenuGeneric2"		"动作 2"
			"Action_MenuGeneric3"		"动作 3"
			"Action_MenuGeneric4"		"动作 4"
			"Action_MenuGeneric5"		"动作 5"
			"Action_MenuLThumb"		"单击左摇杆"
			"Action_MenuLeft"		"左"
			"Action_MenuLookXY"		"次要武器移动"
			"Action_MenuMoveXY"		"移动"
			"Action_MenuNextMenu"		"下一个菜单"
			"Action_MenuPreviousMenu"		"上一个菜单"
			"Action_MenuRThumb"		"单击右摇杆"
			"Action_MenuRight"		"右"
			"Action_MenuSelect"		"选择"
			"Action_MenuToggleChat"		"聊天"
			"Action_MenuUp"		"上"
			"Action_MoveXZ"		"移动"
			"Action_NextPower"		"下一个技能"
			"Action_PlaceMarker"		"放置标记"
			"Action_PowerMenuAbility0"		"技能 A （技能菜单）"
			"Action_PowerMenuAbility1"		"技能 B （技能菜单）"
			"Action_PowerMenuAbility2"		"技能 C （技能菜单）"
			"Action_PowerMenuAbility3"		"技能 D （技能菜单）"
			"Action_PowerMenuAbility4"		"技能 E （技能菜单）"
			"Action_PreviousPower"		"先前技能"
			"Action_QuickMelee"		"快速近战"
			"Action_Reload"		"装填弹匣"
			"Action_ReverseCamera"		"反转镜头"
			"Action_ScoopChannel"		"投掷"
			"Action_ScoopParry"		"吸引球"
			"Action_ScoopPass"		"传球"
			"Action_ScoopSwing"		"牵制"
			"Action_SecondaryFire"		"次要射击模式"
			"Action_ShawzinChangeInstrument"		"更改乐器"
			"Action_ShawzinChangeScale"		"改变缩放比例"
			"Action_ShawzinExit"		"退出"
			"Action_ShawzinFretAltOne"		"替换天品"
			"Action_ShawzinFretAltThree"		"替换水品"
			"Action_ShawzinFretAltTwo"		"替换地品"
			"Action_ShawzinFretFour"		"和弦"
			"Action_ShawzinFretOne"		"天品"
			"Action_ShawzinFretThree"		"水品"
			"Action_ShawzinFretTwo"		"地品"
			"Action_ShawzinNoteOne"		"第一条弦"
			"Action_ShawzinNoteThree"		"第三条弦"
			"Action_ShawzinNoteTwo"		"第二条弦"
			"Action_ShawzinNoteWhammy"		"哇音"
			"Action_ShawzinSongList"		"歌曲列表"
			"Action_ShawzinStepSequencer"		"编入音序器"
			"Action_ShowConsumablesMenu"		"物品弹窗"
			"Action_ShowLevelMap"		"显示地图"
			"Action_ShowPauseMenu"		"显示暂停菜单"
			"Action_ShowPowerMenu"		"技能选单"
			"Action_SlowMovement"		"慢动作"
			"Action_Sprint"		"冲刺"
			"Action_SwitchWeapon"		"切换武器"
			"Action_TiltYaw"		"向右倾斜"
			"Action_TiltYawInvert"		"向左倾斜"
			"Action_ToggleFreeCamera"		"切换自由镜头"
			"Action_UseGearItem0"		"物品热键 1"
			"Action_UseGearItem1"		"物品热键 2"
			"Action_UseGearItem2"		"物品热键 3"
			"Action_UseGearItem3"		"物品热键 4"
			"Action_UseGearItem4"		"物品热键 5"
			"Action_UseGearItem5"		"物品热键 6"
			"Action_UseGearItem6"		"物品热键 7"
			"Action_UseGearItem7"		"物品热键 8"
			"Action_UsePower"		"使用选中的技能"
			"Action_ViewHumanPlayers"		"显示玩家列表"
			"Action_ViewRailjackSystems"		"查看航道星舰系统"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"装饰"
			"Set_FrameFighter"		"战甲霸王"
			"Set_Launcher"		"启动器"
			"Set_Lunaro"		"月动球"
			"Set_Menu"		"菜单"
			"Set_Shawzin"		"三线琴"
			"Set_Warframe"		"游戏"
			"Set_WarframeMelee"		"近战模式"
		}
		"koreana"
		{
			"Action_AccelY"		"상승"
			"Action_AccelYInvert"		"하강"
			"Action_ActivateAbility0"		"어빌리티 A"
			"Action_ActivateAbility1"		"어빌리티 B"
			"Action_ActivateAbility2"		"어빌리티 C"
			"Action_ActivateAbility3"		"어빌리티 D"
			"Action_ActivateAbility4"		"어빌리티 E"
			"Action_AimWeapon"		"무기 조준"
			"Action_ArchwingAscend"		"위로 이동 (아크윙/장식물 배치)"
			"Action_ArchwingDescend"		"아래로 이동 (아크윙/장식물 배치)"
			"Action_CapturaAdvanceTime"		"장면 앞당기기"
			"Action_CapturaExit"		"자유 시점 카메라 나가기"
			"Action_CapturaPanelVisibility"		"토글 패널 보이기"
			"Action_CapturaToggleControl"		"설정"
			"Action_ContextAction"		"상호작용"
			"Action_Crouch"		"앉기, 슬라이드, 구르기"
			"Action_DecorationAdd"		"새 장식물 추가하기"
			"Action_DecorationEdit"		"장식물 수정하기"
			"Action_DecorationExit"		"뒤로가기"
			"Action_DecorationMode"		"고급 모드"
			"Action_DecorationPlace"		"장식물 배치하기"
			"Action_DecorationPushPull"		"장식물 밀기/당기기"
			"Action_DecorationReset"		"초기화"
			"Action_DecorationRotate"		"길게 눌러서 회전"
			"Action_DecorationRotation"		"회전축 변경"
			"Action_DecorationScale"		"길게 눌러 크기 조절"
			"Action_DecorationScaleDown"		"축소"
			"Action_DecorationScaleUp"		"확대"
			"Action_DecorationSnap"		"스냅"
			"Action_DecorationSurfaceSnap"		"평면에 맞추기"
			"Action_EquipRailjackRepairTool"		"옴니 장착"
			"Action_FastMovement"		"빠른 이동"
			"Action_FighterGrab"		"잡기"
			"Action_FighterMoveDown"		"앉기"
			"Action_FighterMoveLeft"		"왼쪽으로 이동"
			"Action_FighterMoveRight"		"오른쪽으로 이동"
			"Action_FighterMoveUp"		"점프"
			"Action_FighterParry"		"막기"
			"Action_FighterPower"		"특수"
			"Action_FighterSwing"		"약 공격"
			"Action_FighterSwingHeavy"		"강 공격"
			"Action_FighterSwingMedium"		"중 공격"
			"Action_FireWeapon"		"무기 발사"
			"Action_HidePauseMenu"		"일시 정지 메뉴 숨기기"
			"Action_Inspect"		"조사하기"
			"Action_Jump"		"점프"
			"Action_LookXY"		"보기"
			"Action_MeleeChannel"		"근접 채널"
			"Action_MenuBack"		"뒤로가기"
			"Action_MenuDown"		"내려가기"
			"Action_MenuGeneric1"		"액션 1"
			"Action_MenuGeneric2"		"액션 2"
			"Action_MenuGeneric3"		"액션 3"
			"Action_MenuGeneric4"		"액션 4"
			"Action_MenuGeneric5"		"액션 5"
			"Action_MenuLThumb"		"왼쪽 스틱 클릭"
			"Action_MenuLeft"		"왼쪽"
			"Action_MenuLookXY"		"보조 이동"
			"Action_MenuMoveXY"		"이동"
			"Action_MenuNextMenu"		"다음 메뉴"
			"Action_MenuPreviousMenu"		"이전 메뉴"
			"Action_MenuRThumb"		"오른쪽 스틱 클릭"
			"Action_MenuRight"		"오른쪽"
			"Action_MenuSelect"		"선택"
			"Action_MenuToggleChat"		"채팅"
			"Action_MenuUp"		"위"
			"Action_MoveXZ"		"이동"
			"Action_NextPower"		"다음 어빌리티"
			"Action_PlaceMarker"		"마커 배치"
			"Action_PowerMenuAbility0"		"어빌리티 A (어빌리티 메뉴)"
			"Action_PowerMenuAbility1"		"어빌리티 B (어빌리티 메뉴)"
			"Action_PowerMenuAbility2"		"어빌리티 C (어빌리티 메뉴)"
			"Action_PowerMenuAbility3"		"어빌리티 D (어빌리티 메뉴)"
			"Action_PowerMenuAbility4"		"어빌리티 E (어빌리티 메뉴)"
			"Action_PreviousPower"		"이전 어빌리티"
			"Action_QuickMelee"		"빠른 근접 공격"
			"Action_Reload"		"재장전"
			"Action_ReverseCamera"		"카메라 전환"
			"Action_ScoopChannel"		"던지기"
			"Action_ScoopParry"		"공 끌어당기기"
			"Action_ScoopPass"		"패스"
			"Action_ScoopSwing"		"체크"
			"Action_SecondaryFire"		"보조 발사"
			"Action_ShawzinChangeInstrument"		"악기 변경"
			"Action_ShawzinChangeScale"		"음계 변경"
			"Action_ShawzinExit"		"나가기"
			"Action_ShawzinFretAltOne"		"하늘 프렛 대체 조작"
			"Action_ShawzinFretAltThree"		"물 프렛 대체 조작"
			"Action_ShawzinFretAltTwo"		"땅 프렛 대체 조작"
			"Action_ShawzinFretFour"		"프렛 화음"
			"Action_ShawzinFretOne"		"하늘 프렛"
			"Action_ShawzinFretThree"		"물 프렛"
			"Action_ShawzinFretTwo"		"땅 프렛"
			"Action_ShawzinNoteOne"		"1번 현"
			"Action_ShawzinNoteThree"		"3번 현"
			"Action_ShawzinNoteTwo"		"2번 현"
			"Action_ShawzinNoteWhammy"		"아밍 음"
			"Action_ShawzinSongList"		"노래 목록"
			"Action_ShawzinStepSequencer"		"따라 연주하기"
			"Action_ShowConsumablesMenu"		"아이템 팝업"
			"Action_ShowLevelMap"		"높이 정보 지도 표시"
			"Action_ShowPauseMenu"		"일시 정지 메뉴 열기"
			"Action_ShowPowerMenu"		"어빌리티 메뉴"
			"Action_SlowMovement"		"느리게 이동"
			"Action_Sprint"		"질주"
			"Action_SwitchWeapon"		"무기 변경"
			"Action_TiltYaw"		"오른쪽으로 기울기"
			"Action_TiltYawInvert"		"왼쪽으로 기울기"
			"Action_ToggleFreeCamera"		"자유 시점 카메라 토글"
			"Action_UseGearItem0"		"소모품 단축키 1"
			"Action_UseGearItem1"		"소모품 단축키 2"
			"Action_UseGearItem2"		"소모품 단축키 3"
			"Action_UseGearItem3"		"소모품 단축키 4"
			"Action_UseGearItem4"		"소모품 단축키 5"
			"Action_UseGearItem5"		"소모품 단축키 6"
			"Action_UseGearItem6"		"소모품 단축키 7"
			"Action_UseGearItem7"		"소모품 단축키 8"
			"Action_UsePower"		"선택한 어빌리티 사용"
			"Action_ViewHumanPlayers"		"플레이어 목록 보이기"
			"Action_ViewRailjackSystems"		"레일잭 시스템 보기"
			"Set_Captura"		"캡처라"
			"Set_Decoration"		"장식물"
			"Set_FrameFighter"		"프레임 파이터"
			"Set_Launcher"		"런처"
			"Set_Lunaro"		"루나로"
			"Set_Menu"		"메뉴"
			"Set_Shawzin"		"샤진"
			"Set_Warframe"		"게임"
			"Set_WarframeMelee"		"근접 모드"
		}
		"tchinese"
		{
			"Action_AccelY"		"升高"
			"Action_AccelYInvert"		"降低"
			"Action_ActivateAbility0"		"技能 A"
			"Action_ActivateAbility1"		"技能 B"
			"Action_ActivateAbility2"		"技能 C"
			"Action_ActivateAbility3"		"技能 D"
			"Action_ActivateAbility4"		"技能 E"
			"Action_AimWeapon"		"瞄準武器"
			"Action_ArchwingAscend"		"向上移動（Archwing/裝飾）"
			"Action_ArchwingDescend"		"向下移動（Archwing/裝飾）"
			"Action_CapturaAdvanceTime"		"時間推進"
			"Action_CapturaExit"		"退出自由視角"
			"Action_CapturaPanelVisibility"		"切換面板可視度"
			"Action_CapturaToggleControl"		"設定"
			"Action_ContextAction"		"場景互動"
			"Action_Crouch"		"蹲下，滑行，並翻滾。"
			"Action_DecorationAdd"		"新增新裝飾"
			"Action_DecorationEdit"		"編輯裝飾"
			"Action_DecorationExit"		"返回"
			"Action_DecorationMode"		"進階模式"
			"Action_DecorationPlace"		"放置裝置"
			"Action_DecorationPushPull"		"推遠/拉近裝飾"
			"Action_DecorationReset"		"重置"
			"Action_DecorationRotate"		"長按以旋轉"
			"Action_DecorationRotation"		"改變旋轉軸"
			"Action_DecorationScale"		"按住以縮放"
			"Action_DecorationScaleDown"		"縮小"
			"Action_DecorationScaleUp"		"放大"
			"Action_DecorationSnap"		"吸附"
			"Action_DecorationSurfaceSnap"		"表面吸附"
			"Action_EquipRailjackRepairTool"		"裝備奧寧"
			"Action_FastMovement"		"快動作"
			"Action_FighterGrab"		"抓取"
			"Action_FighterMoveDown"		"蹲下"
			"Action_FighterMoveLeft"		"向左移動"
			"Action_FighterMoveRight"		"向右移動"
			"Action_FighterMoveUp"		"跳躍"
			"Action_FighterParry"		"格擋"
			"Action_FighterPower"		"特殊"
			"Action_FighterSwing"		"輕擊"
			"Action_FighterSwingHeavy"		"重擊"
			"Action_FighterSwingMedium"		"中擊"
			"Action_FireWeapon"		"開火"
			"Action_HidePauseMenu"		"隱藏暫停選單"
			"Action_Inspect"		"檢視"
			"Action_Jump"		"跳躍"
			"Action_LookXY"		"造型"
			"Action_MeleeChannel"		"近戰導引"
			"Action_MenuBack"		"返回"
			"Action_MenuDown"		"下"
			"Action_MenuGeneric1"		"動作 1"
			"Action_MenuGeneric2"		"動作 2"
			"Action_MenuGeneric3"		"動作 3"
			"Action_MenuGeneric4"		"動作 4"
			"Action_MenuGeneric5"		"動作 5"
			"Action_MenuLThumb"		"點擊左搖桿"
			"Action_MenuLeft"		"左"
			"Action_MenuLookXY"		"次要武器移動"
			"Action_MenuMoveXY"		"移動"
			"Action_MenuNextMenu"		"下一個選單"
			"Action_MenuPreviousMenu"		"上一個選單"
			"Action_MenuRThumb"		"點擊右搖桿"
			"Action_MenuRight"		"右"
			"Action_MenuSelect"		"選擇"
			"Action_MenuToggleChat"		"聊天"
			"Action_MenuUp"		"上"
			"Action_MoveXZ"		"移動"
			"Action_NextPower"		"下一個技能"
			"Action_PlaceMarker"		"放置標記"
			"Action_PlayEmote0"		" "
			"Action_PlayEmote1"		" "
			"Action_PlayEmote10"		" "
			"Action_PlayEmote11"		" "
			"Action_PlayEmote2"		" "
			"Action_PlayEmote3"		" "
			"Action_PlayEmote4"		" "
			"Action_PlayEmote5"		" "
			"Action_PlayEmote6"		" "
			"Action_PlayEmote7"		" "
			"Action_PlayEmote8"		" "
			"Action_PlayEmote9"		" "
			"Action_PowerMenuAbility0"		"技能 A（技能選單）"
			"Action_PowerMenuAbility1"		"技能 B（技能選單）"
			"Action_PowerMenuAbility2"		"技能 C（技能選單）"
			"Action_PowerMenuAbility3"		"技能 D（技能選單）"
			"Action_PowerMenuAbility4"		"技能 E（技能選單）"
			"Action_PreviousPower"		"上一個技能"
			"Action_QuickMelee"		"快速近戰"
			"Action_Reload"		"裝填"
			"Action_ReverseCamera"		"切換越肩視角"
			"Action_ScoopChannel"		"投擲"
			"Action_ScoopParry"		"吸球"
			"Action_ScoopPass"		"傳球"
			"Action_ScoopSwing"		"牽制"
			"Action_SecondaryFire"		"次要射擊模式"
			"Action_ShawzinChangeInstrument"		"更換樂器"
			"Action_ShawzinChangeScale"		"改變縮放比例"
			"Action_ShawzinExit"		"退出"
			"Action_ShawzinFretAltOne"		"天品替換"
			"Action_ShawzinFretAltThree"		"地品替換"
			"Action_ShawzinFretAltTwo"		"地品替換"
			"Action_ShawzinFretFour"		"和弦"
			"Action_ShawzinFretOne"		"天品"
			"Action_ShawzinFretThree"		"水品"
			"Action_ShawzinFretTwo"		"地品"
			"Action_ShawzinNoteOne"		"第一條弦"
			"Action_ShawzinNoteThree"		"第三條弦"
			"Action_ShawzinNoteTwo"		"第二條弦"
			"Action_ShawzinNoteWhammy"		"Whammy 音符"
			"Action_ShawzinSongList"		"歌曲列表"
			"Action_ShawzinStepSequencer"		"音序器"
			"Action_ShowConsumablesMenu"		"物品彈出"
			"Action_ShowLevelMap"		"顯示地圖"
			"Action_ShowPauseMenu"		"顯示暫停選單"
			"Action_ShowPowerMenu"		"技能選單"
			"Action_SlowMovement"		"慢動作"
			"Action_Sprint"		"衝刺"
			"Action_SwitchWeapon"		"切換武器"
			"Action_TiltYaw"		"向右傾斜"
			"Action_TiltYawInvert"		"向左傾斜"
			"Action_ToggleFreeCamera"		"切換自由鏡頭"
			"Action_UseGearItem0"		"裝備熱鍵 1"
			"Action_UseGearItem1"		"裝備熱鍵 2"
			"Action_UseGearItem10"		" "
			"Action_UseGearItem11"		" "
			"Action_UseGearItem2"		"裝備熱鍵 3"
			"Action_UseGearItem3"		"裝備熱鍵 4"
			"Action_UseGearItem4"		"裝備熱鍵 5"
			"Action_UseGearItem5"		"裝備熱鍵 6"
			"Action_UseGearItem6"		"裝備熱鍵 7"
			"Action_UseGearItem7"		"裝備熱鍵 8"
			"Action_UseGearItem8"		" "
			"Action_UseGearItem9"		" "
			"Action_UsePower"		"使用已選技能"
			"Action_ViewHumanPlayers"		"顯示玩家列表"
			"Action_ViewRailjackSystems"		"查看銳捷號系統"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"裝飾"
			"Set_FrameFighter"		"戰甲快打"
			"Set_Launcher"		"啟動器"
			"Set_Lunaro"		"月動球"
			"Set_Menu"		"選單"
			"Set_Shawzin"		"三線琴"
			"Set_Warframe"		"遊戲"
			"Set_WarframeMelee"		"近戰模式"
		}
		"thai"
		{
			"Action_AccelY"		"ขึ้น"
			"Action_AccelYInvert"		"ลง"
			"Action_ActivateAbility0"		"พลัง A"
			"Action_ActivateAbility1"		"พลัง B"
			"Action_ActivateAbility2"		"พลัง C"
			"Action_ActivateAbility3"		"พลัง D"
			"Action_ActivateAbility4"		"พลัง E"
			"Action_AimWeapon"		"เล็งอาวุธ"
			"Action_ArchwingAscend"		"เลื่อนขึ้น (Archwing/ของตกแต่ง)"
			"Action_ArchwingDescend"		"เลื่อนลง (Archwing/ของตกแต่ง)"
			"Action_CapturaAdvanceTime"		"เวลาล่วงหน้า"
			"Action_CapturaExit"		"ออกจากโหมดกล้องอิสระ"
			"Action_CapturaPanelVisibility"		"สลับการมองเห็นแผงควบคุม"
			"Action_CapturaToggleControl"		"การตั้งค่า"
			"Action_ContextAction"		"การดำเนินการไปตามบริบท"
			"Action_Crouch"		"ย่อตัว ไถลตัว และม้วนตัว"
			"Action_DecorationAdd"		"เพิ่มของตกแต่งใหม่"
			"Action_DecorationEdit"		"ปรับเปลี่ยนของตกแต่ง"
			"Action_DecorationExit"		"กลับ"
			"Action_DecorationMode"		"โหมดขั้นสูง"
			"Action_DecorationPlace"		"วางของตกแต่ง"
			"Action_DecorationPushPull"		"ผลัก/ดึงของตกแต่ง"
			"Action_DecorationReset"		"รีเซ็ต"
			"Action_DecorationRotate"		"กดค้างไว้เพื่อหมุน"
			"Action_DecorationRotation"		"เปลี่ยนแกนหมุน"
			"Action_DecorationScale"		"กดค้างไว้เพื่อปรับขนาด"
			"Action_DecorationScaleDown"		"ลดขนาดลง"
			"Action_DecorationScaleUp"		"ปรับขนาดขึ้น"
			"Action_DecorationSnap"		"สแนป"
			"Action_DecorationSurfaceSnap"		"การสแนปติดพื้นผิว"
			"Action_EquipRailjackRepairTool"		"ติดตั้งออมนิ"
			"Action_FastMovement"		"เคลื่อนไหวอย่างรวดเร็ว"
			"Action_FighterGrab"		"คว้า"
			"Action_FighterMoveDown"		"ย่อตัว"
			"Action_FighterMoveLeft"		"เลื่อนไปทางซ้าย"
			"Action_FighterMoveRight"		"เลื่อนไปทางขวา"
			"Action_FighterMoveUp"		"กระโดด"
			"Action_FighterParry"		"บล็อก"
			"Action_FighterPower"		"พิเศษ"
			"Action_FighterSwing"		"โจมตีเบา"
			"Action_FighterSwingHeavy"		"โจมตีหนัก"
			"Action_FighterSwingMedium"		"โจมตีปานกลาง"
			"Action_FireWeapon"		"ยิงอาวุธ"
			"Action_HidePauseMenu"		"ซ่อนเมนูหยุดชั่วคราว"
			"Action_Inspect"		"ตรวจสอบ"
			"Action_Jump"		"กระโดด"
			"Action_LookXY"		"ดู"
			"Action_MeleeChannel"		"แชนเนลระยะประชิด"
			"Action_MenuBack"		"กลับ"
			"Action_MenuDown"		"ลง"
			"Action_MenuGeneric1"		"ปุ่มแอ็กชัน 1"
			"Action_MenuGeneric2"		"ปุ่มแอ็กชัน 2"
			"Action_MenuGeneric3"		"ปุ่มแอ็กชัน 3"
			"Action_MenuGeneric4"		"ปุ่มแอ็กชัน 4"
			"Action_MenuGeneric5"		"ปุ่มแอ็กชัน 5"
			"Action_MenuLThumb"		"คลิกจอยสติ๊กอนาล็อกซ้าย"
			"Action_MenuLeft"		"ซ้าย"
			"Action_MenuLookXY"		"เคลื่อนไหวรอง"
			"Action_MenuMoveXY"		"เคลื่อนตัว"
			"Action_MenuNextMenu"		"เมนูถัดไป"
			"Action_MenuPreviousMenu"		"เมนูก่อนหน้า"
			"Action_MenuRThumb"		"คลิกจอยสติ๊กอนาล็อกขวา"
			"Action_MenuRight"		"ขวา"
			"Action_MenuSelect"		"เลือก"
			"Action_MenuToggleChat"		"Chat"
			"Action_MenuUp"		"ขึ้น"
			"Action_MoveXZ"		"เคลื่อนตัว"
			"Action_NextPower"		"พลังถัดไป"
			"Action_PlaceMarker"		"ปักหมุด"
			"Action_PlayEmote0"		" "
			"Action_PlayEmote1"		" "
			"Action_PlayEmote10"		" "
			"Action_PlayEmote11"		" "
			"Action_PlayEmote2"		" "
			"Action_PlayEmote3"		" "
			"Action_PlayEmote4"		" "
			"Action_PlayEmote5"		" "
			"Action_PlayEmote6"		" "
			"Action_PlayEmote7"		" "
			"Action_PlayEmote8"		" "
			"Action_PlayEmote9"		" "
			"Action_PowerMenuAbility0"		"พลัง A (เมนูพลังความสามารถ)"
			"Action_PowerMenuAbility1"		"พลัง B (เมนูพลังความสามารถ)"
			"Action_PowerMenuAbility2"		"พลัง C (เมนูพลังความสามารถ)"
			"Action_PowerMenuAbility3"		"พลัง D (เมนูพลังความสามารถ)"
			"Action_PowerMenuAbility4"		"พลัง E (เมนูพลังความสามารถ)"
			"Action_PreviousPower"		"พลังก่อนหน้า"
			"Action_QuickMelee"		"โจมตีระยะประชิดอย่างรวดเร็ว"
			"Action_Reload"		"รีโหลด"
			"Action_ReverseCamera"		"กล้องกลับด้าน"
			"Action_ScoopChannel"		"ขว้าง"
			"Action_ScoopParry"		"ดึงดูดลูกบอล"
			"Action_ScoopPass"		"ส่งบอล"
			"Action_ScoopSwing"		"เช็ก"
			"Action_SecondaryFire"		"การยิงสำรอง"
			"Action_ShawzinChangeInstrument"		"เปลี่ยนเครื่องดนตรี"
			"Action_ShawzinChangeScale"		"เปลี่ยนสเกล"
			"Action_ShawzinExit"		"ออก"
			"Action_ShawzinFretAltOne"		"เฟรตฟ้า (Alt)"
			"Action_ShawzinFretAltThree"		"เฟรตน้ำ (Alt)"
			"Action_ShawzinFretAltTwo"		"เฟรตโลก (Alt)"
			"Action_ShawzinFretFour"		"เฟรตที่สี่"
			"Action_ShawzinFretOne"		"เฟรตฟ้า"
			"Action_ShawzinFretThree"		"เฟรตน้ำ"
			"Action_ShawzinFretTwo"		"เฟรตโลก"
			"Action_ShawzinNoteOne"		"สตริงที่ 1"
			"Action_ShawzinNoteThree"		"สตริงที่ 3"
			"Action_ShawzinNoteTwo"		"สตริงที่ 2"
			"Action_ShawzinNoteWhammy"		"Whammy"
			"Action_ShawzinSongList"		"รายชื่อเพลง"
			"Action_ShawzinStepSequencer"		"ซีเควนเซอร์"
			"Action_ShowConsumablesMenu"		"ป๊อปอัปไอเทมขึ้นมา"
			"Action_ShowLevelMap"		"แสดงแผนที่"
			"Action_ShowPauseMenu"		"แสดงเมนูหยุดชั่วคราว"
			"Action_ShowPowerMenu"		"เมนูพลังความสามารถ"
			"Action_SlowMovement"		"เคลื่อนไหวช้า"
			"Action_Sprint"		"วิ่งเร็ว"
			"Action_SwitchWeapon"		"เปลี่ยนอาวุธ"
			"Action_TiltYaw"		"เอียงขวา"
			"Action_TiltYawInvert"		"เอียงซ้าย"
			"Action_ToggleFreeCamera"		"สลับกล้องอิสระ"
			"Action_UseGearItem0"		"คีย์ลัดเกียร์ 1"
			"Action_UseGearItem1"		"คีย์ลัดเกียร์ 2"
			"Action_UseGearItem10"		" "
			"Action_UseGearItem11"		" "
			"Action_UseGearItem2"		"คีย์ลัดเกียร์ 3"
			"Action_UseGearItem3"		"คีย์ลัดเกียร์ 4"
			"Action_UseGearItem4"		"คีย์ลัดเกียร์ 5"
			"Action_UseGearItem5"		"คีย์ลัดเกียร์ 6"
			"Action_UseGearItem6"		"คีย์ลัดเกียร์ 7"
			"Action_UseGearItem7"		"คีย์ลัดเกียร์ 8"
			"Action_UseGearItem8"		" "
			"Action_UseGearItem9"		" "
			"Action_UsePower"		"ใช้พลังที่เลือก"
			"Action_ViewHumanPlayers"		"แสดงรายชื่อผู้เล่น"
			"Action_ViewRailjackSystems"		"ดูระบบของเรลแจ็ก"
			"Set_Captura"		"Captura"
			"Set_Decoration"		"การตกแต่ง"
			"Set_FrameFighter"		"Frame Fighter"
			"Set_Launcher"		"Launcher"
			"Set_Lunaro"		"Lunaro"
			"Set_Menu"		"เมนู"
			"Set_Shawzin"		"Shawzin"
			"Set_Warframe"		"เกม"
			"Set_WarframeMelee"		"โหมดระยะประชิด"
		}
	}
	"group"
	{
		"id"		"0"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE, Jump, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press Q, Use, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press B, Reload, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press R, Flashlight, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"button_size"		"17994"
			"button_dist"		"19994"
		}
	}
	"group"
	{
		"id"		"1"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1, Weapon 1, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3, Weapon 3, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2, Weapon 2, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4, Weapon 4, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"edge_binding_radius"		"24996"
		}
	}
	"group"
	{
		"id"		"2"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button BACK, Use, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"400"
			"friction"		"1"
			"friction_vert_scale"		"200"
			"mouse_move_threshold"		"3"
			"doubetap_max_duration"		"320"
		}
	}
	"group"
	{
		"id"		"3"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press W, Move Forward, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press S, Move Back, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press D, Move Right, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press A, Move Left, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_SHIFT, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press BACKSPACE, Sprint, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"requires_click"		"0"
			"edge_binding_radius"		"24995"
		}
	}
	"group"
	{
		"id"		"4"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"5"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button LEFT, , "
						}
						"settings"
						{
							"repeat_rate"		"99"
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"9"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_natural_sensitivity"		"200"
			"gyro_ratchet_button_mask"		"34603048"
		}
	}
	"group"
	{
		"id"		"11"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 5, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"300"
		}
	}
	"group"
	{
		"id"		"13"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"14"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"15"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"18"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"20"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press E, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"22"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 5, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"24"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press N, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F11, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F8, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F1, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F12, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"6"
		"mode"		"switches"
		"name"		""
		"description"		"#Description"
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press ESCAPE, Menu, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_menu"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press TAB, Map, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"controller_action hold_layer 2 0 0, Previous Weapon, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button MIDDLE, Next Weapon, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_left"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_CONTROL, Crouch, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_right"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE, Reload, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"16"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press M, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_menu"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press P, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button FORWARD, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"preset"
	{
		"id"		"0"
		"name"		"Default"
		"group_source_bindings"
		{
			"6"		"switch active"
			"0"		"button_diamond active"
			"1"		"left_trackpad inactive"
			"11"		"left_trackpad active"
			"2"		"right_trackpad active"
			"3"		"joystick active"
			"4"		"left_trigger active"
			"5"		"right_trigger active"
			"9"		"gyro active"
		}
	}
	"preset"
	{
		"id"		"1"
		"name"		"Preset_1000001"
		"group_source_bindings"
		{
			"16"		"switch active"
			"13"		"button_diamond active"
			"24"		"left_trackpad active"
			"20"		"right_trackpad active"
			"22"		"joystick active"
			"14"		"left_trigger active"
			"15"		"right_trigger active"
			"18"		"gyro active"
		}
	}
	"settings"
	{
		"left_trackpad_mode"		"0"
		"right_trackpad_mode"		"0"
	}
}
