"controller_mappings"
{
	"version"		"3"
	"revision"		"45"
	"title"		"#Title"
	"description"		"#SettingsController_AutosaveDescription"
	"creator"		"76561198451963945"
	"progenitor"		"game://config_controller_ps4.vdf"
	"url"		"autosave://C:\\Steam\\steamapps\\common\\Steam Controller Configs\\354592202\\config\\3065170\\controller_ps4.vdf"
	"export_type"		"personal_cloud"
	"controller_type"		"controller_ps4"
	"controller_caps"		"35085311"
	"major_revision"		"0"
	"minor_revision"		"0"
	"Timestamp"		"0"
	"actions"
	{
		"controls"
		{
			"title"		"#Set_Controls"
			"legacy_set"		"0"
			"StickPadGyro"
			{
				"AxisL"
				{
					"title"		"#Action_AxisL"
					"input_mode"		"joystick_move"
				}
				"AxisR"
				{
					"title"		"#Action_AxisR"
					"input_mode"		"joystick_move"
				}
				"TouchPad"
				{
					"title"		"#Action_TouchPad"
					"input_mode"		"joystick_move"
				}
			}
			"AnalogTrigger"
			{
				"AnalogL"		"#Action_AnalogL"
				"AnalogR"		"#Action_AnalogR"
			}
			"Button"
			{
				"LUp"		"#Action_LUp"
				"LDown"		"#Action_LDown"
				"LLeft"		"#Action_LLeft"
				"LRight"		"#Action_LRight"
				"RUp"		"#Action_RUp"
				"RDown"		"#Action_RDown"
				"RLeft"		"#Action_RLeft"
				"RRight"		"#Action_RRight"
				"CLeft"		"#Action_CLeft"
				"CRight"		"#Action_CRight"
				"CCenter"		"#Action_CCenter"
				"LStickPush"		"#Action_LStickPush"
				"RStickPush"		"#Action_RStickPush"
				"LTrigTop"		"#Action_LTrigTop"
				"RTrigTop"		"#Action_RTrigTop"
			}
		}
	}
	"action_layers"
	{
	}
	"localization"
	{
		"english"
		{
			"title"		"Default"
			"description"		"Layout created by Capcom for use with the PlayStation 4 controller."
			"Set_Controls"		"Controls"
			"Action_AxisL"		"Left Stick"
			"Action_AxisR"		"Right Stick"
			"Action_TouchPad"		"Touch Pad"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Up Button"
			"Action_LDown"		"Down Button"
			"Action_LLeft"		"Left Button"
			"Action_LRight"		"Right Button"
			"Action_RUp"		"Triangle Button"
			"Action_RDown"		"Cross Button"
			"Action_RLeft"		"Square Button"
			"Action_RRight"		"Circle Button"
			"Action_CLeft"		"Create Button"
			"Action_CRight"		"Options Button"
			"Action_CCenter"		"Touch Pad Button"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"japanese"
		{
			"title"		"デフォルト設定"
			"description"		"これはPlayStation4コントローラー用のCapcom公式レイアウトです。"
			"Set_Controls"		"コントロール"
			"Action_AxisL"		"左スティック"
			"Action_AxisR"		"右スティック"
			"Action_TouchPad"		"タッチパッド"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"上キー"
			"Action_LDown"		"下キー"
			"Action_LLeft"		"左キー"
			"Action_LRight"		"右キー"
			"Action_RUp"		"TRIANGLE"
			"Action_RDown"		"CROSS"
			"Action_RLeft"		"SQUARE"
			"Action_RRight"		"CIRCLE"
			"Action_CLeft"		"CREATE"
			"Action_CRight"		"OPTIONS"
			"Action_CCenter"		"タッチパッド押し込み"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"french"
		{
			"title"		"Par défaut"
			"description"		"Configuration créée par Capcom pour la manette PlayStation 4."
			"Set_Controls"		"Commandes"
			"Action_AxisL"		"Joystick gauche"
			"Action_AxisR"		"Joystick droit"
			"Action_TouchPad"		"Pavé tactile"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Touche haut"
			"Action_LDown"		"Touche bas"
			"Action_LLeft"		"Touche gauche"
			"Action_LRight"		"Touche droite"
			"Action_RUp"		"Touche triangle"
			"Action_RDown"		"Touche croix"
			"Action_RLeft"		"Touche carré"
			"Action_RRight"		"Touche cercle"
			"Action_CLeft"		"Touche de création"
			"Action_CRight"		"Touche d'options"
			"Action_CCenter"		"Touche pavé tactile"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"italian"
		{
			"title"		"Predefinito"
			"description"		"Configurazione per controller PlayStation 4 realizzata da Capcom."
			"Set_Controls"		"Comandi"
			"Action_AxisL"		"Levetta sinistra"
			"Action_AxisR"		"Levetta destra"
			"Action_TouchPad"		"Touch pad"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Tasto su"
			"Action_LDown"		"Tasto giù"
			"Action_LLeft"		"Tasto sinistra"
			"Action_LRight"		"Tasto destra"
			"Action_RUp"		"Tasto triangolo"
			"Action_RDown"		"Tasto croce"
			"Action_RLeft"		"Tasto quadrato"
			"Action_RRight"		"Tasto cerchio"
			"Action_CLeft"		"Tasto crea"
			"Action_CRight"		"Tasto Options"
			"Action_CCenter"		"Pulsante touch pad"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"german"
		{
			"title"		"Standard"
			"description"		"Von Capcom erstellte Tastenbelegung für den PlayStation 4-Controller."
			"Set_Controls"		"Steuerung"
			"Action_AxisL"		"Linker Stick"
			"Action_AxisR"		"Rechter Stick"
			"Action_TouchPad"		"Touchpad"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Oben-Taste"
			"Action_LDown"		"Unten-Taste"
			"Action_LLeft"		"Links-Taste"
			"Action_LRight"		"Rechts-Taste"
			"Action_RUp"		"Dreieck-Taste"
			"Action_RDown"		"Kreuz-Taste"
			"Action_RLeft"		"Quadrat-Taste"
			"Action_RRight"		"Kreis-Taste"
			"Action_CLeft"		"Create-Taste"
			"Action_CRight"		"Optionstaste"
			"Action_CCenter"		"Touchpad-Taste"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"spanish"
		{
			"title"		"Predeterminada"
			"description"		"Disposición creada por Capcom para el mando de Playstation 4."
			"Set_Controls"		"Controles"
			"Action_AxisL"		"Joystick izquierdo"
			"Action_AxisR"		"Joystick derecho"
			"Action_TouchPad"		"Panel táctil"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Botón hacia arriba"
			"Action_LDown"		"Botón hacia abajo"
			"Action_LLeft"		"Botón hacia la izquierda"
			"Action_LRight"		"Botón hacia la derecha"
			"Action_RUp"		"Botón triángulo"
			"Action_RDown"		"Botón equis"
			"Action_RLeft"		"Botón cuadrado"
			"Action_RRight"		"Botón círculo"
			"Action_CLeft"		"Botón de crear"
			"Action_CRight"		"Botón de opciones"
			"Action_CCenter"		"Botón del panel táctil"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"russian"
		{
			"title"		"По умолчанию"
			"description"		"Шаблон, созданный Capcom для контроллера PlayStation 4."
			"Set_Controls"		"Элементы управления"
			"Action_AxisL"		"Левый джойстик"
			"Action_AxisR"		"Правый джойстик"
			"Action_TouchPad"		"Сенсорная панель"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Кнопка вверх"
			"Action_LDown"		"Кнопка вниз"
			"Action_LLeft"		"Кнопка влево"
			"Action_LRight"		"Кнопка вправо"
			"Action_RUp"		"Кнопка «треугольник»"
			"Action_RDown"		"Кнопка «крест»"
			"Action_RLeft"		"Кнопка «квадрат»"
			"Action_RRight"		"Кнопка «круг»"
			"Action_CLeft"		"Кнопка создания"
			"Action_CRight"		"Кнопка параметров"
			"Action_CCenter"		"Кнопка сенсорной панели"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"brazilian"
		{
			"title"		"Padrão"
			"description"		"Layout criado pela Capcom para uso com um controle de Playstation 4."
			"Set_Controls"		"Controles"
			"Action_AxisL"		"Controle Analógico Esquerdo"
			"Action_AxisR"		"Controle Analógico Direito"
			"Action_TouchPad"		"Touch Pad"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Botão para Cima"
			"Action_LDown"		"Botão para Baixo"
			"Action_LLeft"		"Botão para a Esquerda"
			"Action_LRight"		"Botão para a Direita"
			"Action_RUp"		"Botão Triângulo"
			"Action_RDown"		"Botão Xis"
			"Action_RLeft"		"Botão Quadrado"
			"Action_RRight"		"Botão Círculo"
			"Action_CLeft"		"Botão Criar"
			"Action_CRight"		"Botão de Opções"
			"Action_CCenter"		"Botão do Touch Pad"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"schinese"
		{
			"title"		"默认设置"
			"description"		"Capcom为PlayStation 4控制器设计的布局。"
			"Set_Controls"		"控制器"
			"Action_AxisL"		"左操作杆"
			"Action_AxisR"		"右操作杆"
			"Action_TouchPad"		"触摸板"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"上方向键"
			"Action_LDown"		"下方向键"
			"Action_LLeft"		"左方向键"
			"Action_LRight"		"右方向键"
			"Action_RUp"		"三角键"
			"Action_RDown"		"交叉键"
			"Action_RLeft"		"正方键"
			"Action_RRight"		"圆圈键"
			"Action_CLeft"		"创建键"
			"Action_CRight"		"选项键"
			"Action_CCenter"		"按下触摸板"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"tchinese"
		{
			"title"		"預設"
			"description"		"由Capcom為PlayStation 4控制器而設計的配置。"
			"Set_Controls"		"操作"
			"Action_AxisL"		"左操作桿"
			"Action_AxisR"		"右操作桿"
			"Action_TouchPad"		"觸碰板"
			"Action_AnalogL"		"L2按鈕"
			"Action_AnalogR"		"R2按鈕"
			"Action_LUp"		"方向按鈕上"
			"Action_LDown"		"方向按鈕下"
			"Action_LLeft"		"方向按鈕左"
			"Action_LRight"		"方向按鈕右"
			"Action_RUp"		"三角按鈕"
			"Action_RDown"		"交叉按鈕"
			"Action_RLeft"		"正方按鈕"
			"Action_RRight"		"圓圈按鈕"
			"Action_CLeft"		"創建按鈕"
			"Action_CRight"		"選項按鈕"
			"Action_CCenter"		"按壓觸碰板"
			"Action_LStickPush"		"L3按鈕"
			"Action_RStickPush"		"R3按鈕"
			"Action_LTrigTop"		"L1按鈕"
			"Action_RTrigTop"		"R1按鈕"
		}
		"koreana"
		{
			"title"		"디폴트 설정"
			"description"		"PlayStation4 컨트롤러용 Capcom 공식 레이아웃입니다."
			"Set_Controls"		"컨트롤"
			"Action_AxisL"		"왼쪽 스틱"
			"Action_AxisR"		"오른쪽 스틱"
			"Action_TouchPad"		"터치패드"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"위쪽 방향키"
			"Action_LDown"		"아래쪽 방향키"
			"Action_LLeft"		"왼쪽 방향키"
			"Action_LRight"		"오른쪽 방향키"
			"Action_RUp"		"세모 버튼"
			"Action_RDown"		"엑스 버튼"
			"Action_RLeft"		"네모 버튼"
			"Action_RRight"		"동그라미 버튼"
			"Action_CLeft"		"만들기 버튼"
			"Action_CRight"		"옵션 버튼"
			"Action_CCenter"		"터치패드 누르기"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"polish"
		{
			"title"		"Domyślnie"
			"description"		"Układ utworzony przez Capcom do użycia z kontrolerem PlayStation 4."
			"Set_Controls"		"Sterowanie"
			"Action_AxisL"		"Lewy drążek"
			"Action_AxisR"		"Prawy drążek"
			"Action_TouchPad"		"Panel dotykowy"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Przycisk w górę"
			"Action_LDown"		"Przycisk w dół"
			"Action_LLeft"		"Przycisk w lewo"
			"Action_LRight"		"Przycisk w prawo"
			"Action_RUp"		"Przycisk z trójkątem"
			"Action_RDown"		"Przycisk z krzyżykiem"
			"Action_RLeft"		"Przycisk z kwadratem"
			"Action_RRight"		"Przycisk z kółkiem"
			"Action_CLeft"		"Przycisk tworzenia"
			"Action_CRight"		"Przycisk opcji"
			"Action_CCenter"		"Przycisk panelu dotykowego"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
		"latam"
		{
			"title"		"Predeterminado"
			"description"		"Diseño creado por Capcom para el control de PlayStation 4."
			"Set_Controls"		"Controles"
			"Action_AxisL"		"Stick izquierdo"
			"Action_AxisR"		"Stick derecho"
			"Action_TouchPad"		"Touch pad"
			"Action_AnalogL"		"L2"
			"Action_AnalogR"		"R2"
			"Action_LUp"		"Botón hacia arriba"
			"Action_LDown"		"Botón hacia abajo"
			"Action_LLeft"		"Botón hacia la izquierda"
			"Action_LRight"		"Botón hacia la derecha"
			"Action_RUp"		"Botón triángulo"
			"Action_RDown"		"Botón equis"
			"Action_RLeft"		"Botón cuadrado"
			"Action_RRight"		"Botón círculo"
			"Action_CLeft"		"Botón crear"
			"Action_CRight"		"Botón opciones"
			"Action_CCenter"		"Botón del touch pad"
			"Action_LStickPush"		"L3"
			"Action_RStickPush"		"R3"
			"Action_LTrigTop"		"L1"
			"Action_RTrigTop"		"R1"
		}
	}
	"group"
	{
		"id"		"1"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls RDown, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls RRight, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls RLeft, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls RUp, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"2"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"3"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"4"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"virtual_mode"		"1"
		}
		"gameactions"
		{
			"controls"		"AnalogL"
		}
	}
	"group"
	{
		"id"		"5"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"virtual_mode"		"1"
		}
		"gameactions"
		{
			"controls"		"AnalogR"
		}
	}
	"group"
	{
		"id"		"6"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"7"
		"mode"		"touch_menu"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"mouse_smoothing"		"0"
			"gyro_button"		"1"
		}
	}
	"group"
	{
		"id"		"8"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls LUp, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls LDown, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls LRight, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls LLeft, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"requires_click"		"0"
			"haptic_intensity_override"		"0"
		}
	}
	"group"
	{
		"id"		"9"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls LStickPush, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"virtual_mode"		"1"
			"deadzone_enable_type"		"0"
		}
		"gameactions"
		{
			"controls"		"AxisL"
		}
	}
	"group"
	{
		"id"		"10"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls RStickPush, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"virtual_mode"		"1"
			"deadzone_enable_type"		"0"
		}
		"gameactions"
		{
			"controls"		"AxisR"
		}
	}
	"group"
	{
		"id"		"11"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"12"
		"mode"		"touch_menu"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"mouse_smoothing"		"0"
			"gyro_button"		"1"
		}
	}
	"group"
	{
		"id"		"13"
		"mode"		"mouse_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"virtual_mode"		"1"
		}
		"gameactions"
		{
			"controls"		"AxisL"
		}
	}
	"group"
	{
		"id"		"14"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls CCenter, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"virtual_mode"		"1"
			"anti_deadzone_buffer"		"849"
			"deadzone_shape"		"2"
		}
		"gameactions"
		{
			"controls"		"TouchPad"
		}
	}
	"group"
	{
		"id"		"15"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"virtual_mode"		"1"
		}
		"gameactions"
		{
			"controls"		"AxisL"
		}
	}
	"group"
	{
		"id"		"16"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"19"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"requires_click"		"0"
		}
	}
	"group"
	{
		"id"		"20"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"22"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"23"
		"mode"		"gyro_to_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_ratchet_button_mask"		"33554432"
		}
	}
	"group"
	{
		"id"		"25"
		"mode"		"gyro_to_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"26"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_ratchet_button_mask"		"33554432"
		}
	}
	"group"
	{
		"id"		"28"
		"mode"		"gyro_to_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"29"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_ratchet_button_mask"		"33554432"
		}
	}
	"group"
	{
		"id"		"0"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls CRight, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_menu"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls CLeft, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls LTrigTop, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"game_action controls RTrigTop, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"31"
		"mode"		"gyro_to_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_ratchet_button_mask"		"33554432"
		}
	}
	"preset"
	{
		"id"		"0"
		"name"		"controls"
		"group_source_bindings"
		{
			"0"		"switch active"
			"1"		"button_diamond active"
			"2"		"left_trigger inactive"
			"4"		"left_trigger active"
			"3"		"right_trigger inactive"
			"5"		"right_trigger active"
			"9"		"joystick active"
			"6"		"left_trackpad inactive"
			"7"		"left_trackpad inactive"
			"10"		"right_joystick active"
			"8"		"dpad active"
			"11"		"center_trackpad inactive"
			"12"		"center_trackpad inactive"
			"13"		"center_trackpad inactive"
			"14"		"center_trackpad active"
			"15"		"center_trackpad inactive"
			"16"		"center_trackpad inactive"
			"19"		"gyro active modeshift"
			"20"		"gyro inactive"
			"22"		"gyro inactive"
			"23"		"gyro inactive"
			"25"		"gyro inactive"
			"26"		"gyro inactive"
			"28"		"gyro inactive"
			"29"		"gyro inactive"
			"31"		"gyro active"
		}
	}
	"settings"
	{
	}
}
